/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Extended Art Color Spectrum & GSAP Variables */
:root {
    /* Existing colors */
    --primary-red: #ff3333;
    --primary-pink: #ff69b4;
    
    /* Extended art color spectrum */
    --art-purple: #8a2be2;
    --art-blue: #4169e1;
    --art-cyan: #00ffff;
    --art-green: #32cd32;
    --art-yellow: #ffd700;
    --art-orange: #ff8c00;
    --art-magenta: #ff1493;
    --art-violet: #9400d3;
    --art-indigo: #4b0082;
    --art-turquoise: #40e0d0;
    --art-lime: #00ff00;
    --art-coral: #ff7f50;
    
    /* Neutral tones */
    --art-white: #fafafa;
    --art-light-gray: #e0e0e0;
    --art-gray: #808080;
    --art-dark-gray: #404040;
    --art-black: #1a1a1a;
    
    /* Typography Scale - Figma Style */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-2xl: 32px;
    --font-size-3xl: 48px;
    --font-size-4xl: 64px;
    --font-size-5xl: 96px;
    --font-size-6xl: 128px;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* Line Heights */
    --line-height-tight: 1.1;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Letter Spacing */
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;

    /* Spacing Scale - Figma Style */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    --space-12: 48px;
    --space-16: 64px;
    --space-20: 80px;
    --space-24: 96px;
    --space-32: 128px;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 24px;
    --radius-full: 9999px;

    /* Breakpoints */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;

    /* GSAP animation timing */
    --gsap-fast: 0.3s;
    --gsap-medium: 0.6s;
    --gsap-slow: 1.2s;
    --gsap-very-slow: 2.4s;

    /* GSAP easing functions */
    --gsap-ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --gsap-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
    --gsap-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --gsap-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    
    /* Visual effects */
    --blur-light: blur(2px);
    --blur-medium: blur(5px);
    --blur-heavy: blur(10px);
    --glow-soft: 0 0 20px rgba(255, 255, 255, 0.3);
    --glow-medium: 0 0 40px rgba(255, 255, 255, 0.5);
    --glow-strong: 0 0 60px rgba(255, 255, 255, 0.8);
}

/* GSAP Animation Optimization Classes */
.gsap-optimized {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

.gsap-3d {
    transform-style: preserve-3d;
    will-change: transform;
}

.gsap-smooth {
    transition: none !important;
    animation: none !important;
}

html {
    scroll-behavior: smooth;
}

html, body {
    height: 100%;
    cursor: auto;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', sans-serif;
    background: #f5f5f5;
    color: #000;
    position: relative;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Page Loading Animation */
body.loading {
    overflow: hidden;
}

.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity var(--gsap-slow) ease, visibility var(--gsap-slow) ease;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
}

.loader-bars {
    display: flex;
    gap: 4px;
    justify-content: center;
    margin-bottom: 20px;
}

.loader-bar {
    width: 4px;
    height: 30px;
    background: var(--primary-red);
    border-radius: 2px;
    animation: loaderPulse 1.2s infinite ease-in-out;
}

.loader-bar:nth-child(1) { animation-delay: 0s; }
.loader-bar:nth-child(2) { animation-delay: 0.1s; }
.loader-bar:nth-child(3) { animation-delay: 0.2s; }
.loader-bar:nth-child(4) { animation-delay: 0.3s; }
.loader-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes loaderPulse {
    0%, 40%, 100% { transform: scaleY(0.4); opacity: 0.5; }
    20% { transform: scaleY(1); opacity: 1; }
}

.loader-text {
    font-size: 14px;
    color: #666;
    letter-spacing: 2px;
    animation: loaderFade 2s infinite ease-in-out;
}

@keyframes loaderFade {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Mouse Follower Effects */
.mouse-follower {
    position: fixed;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, var(--primary-red) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    opacity: 0.6;
    transition: all 0.1s ease;
    mix-blend-mode: difference;
}

.mouse-trail {
    position: fixed;
    width: 6px;
    height: 6px;
    background: var(--primary-red);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9997;
    opacity: 0.8;
    transition: all 0.2s ease;
}

/* Interactive Elements Glow */
.interactive-glow {
    position: relative;
    overflow: hidden;
}

.interactive-glow::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 51, 51, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--gsap-medium) ease;
    pointer-events: none;
}

.interactive-glow:hover::after {
    width: 200px;
    height: 200px;
}

/* Smooth Scroll Container */
.scroll-container {
    width: 100%;
}

/* Removed Custom Cursor - Using default cursor */

/* Navigation - Figma Style Enhanced */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: var(--space-4) var(--space-8);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(24px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav.scrolled {
    padding: var(--space-3) var(--space-8);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
}

.logo-bars {
    display: flex;
    gap: var(--space-1);
    align-items: flex-end;
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: all var(--gsap-medium) var(--gsap-ease-out);
}

.logo-bars:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: scale(1.05);
}

.bar {
    width: 3px;
    background: #000;
    display: block;
    border-radius: 2px;
    transition: all var(--gsap-medium) var(--gsap-bounce);
    transform-origin: bottom;
}

.logo-bars:hover .bar {
    background: var(--primary-red);
}

.logo-bars:hover .bar:nth-child(1) { transform: scaleY(1.2); }
.logo-bars:hover .bar:nth-child(2) { transform: scaleY(1.1); }
.logo-bars:hover .bar:nth-child(3) { transform: scaleY(1.3); }
.logo-bars:hover .bar:nth-child(4) { transform: scaleY(1.15); }
.logo-bars:hover .bar:nth-child(5) { transform: scaleY(1.25); }

.bar:nth-child(1) { height: 8px; }
.bar:nth-child(2) { height: 12px; }
.bar:nth-child(3) { height: 16px; }
.bar:nth-child(4) { height: 12px; }
.bar:nth-child(5) { height: 8px; }

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    letter-spacing: var(--letter-spacing-wide);
}

.nav-link {
    color: #000;
    text-decoration: none;
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    position: relative;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 51, 51, 0.1), transparent);
    transition: left var(--gsap-medium) var(--gsap-ease-out);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-red);
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    transform: translateX(-50%);
    border-radius: 1px;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-red);
    background: rgba(255, 51, 51, 0.05);
    transform: translateY(-1px);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

.nav-separator {
    color: #ccc;
    font-weight: 300;
    opacity: 0.6;
    transition: opacity var(--gsap-fast) ease;
}

.nav-menu:hover .nav-separator {
    opacity: 0.3;
}

.github-link {
    color: #000;
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    padding: var(--space-2);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.github-link::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 51, 51, 0.1);
    border-radius: 50%;
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    transform: translate(-50%, -50%);
}

.github-link:hover::before {
    width: 40px;
    height: 40px;
}

.github-link:hover {
    color: var(--primary-red);
    transform: scale(1.1) rotate(5deg);
}

.github-link svg {
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    position: relative;
    z-index: 1;
}

.github-link:hover svg {
    transform: scale(1.1);
}

/* Page Sections - Enhanced with Figma-style transitions */
.page-section {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transform: translateY(50px);
    transition: all var(--gsap-slow) var(--gsap-ease-out);
}

.page-section.in-view {
    opacity: 1;
    transform: translateY(0);
}

.page-section.leaving {
    opacity: 0;
    transform: translateY(-30px);
}

/* Staggered animation for section content */
.page-section .content {
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    transition-delay: 0.2s;
}

.page-section.in-view .content {
    opacity: 1;
    transform: translateY(0);
}

/* Scroll Progress Indicator - Figma Style */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-red), var(--art-orange), var(--art-yellow));
    z-index: 9999;
    transition: width 0.15s var(--gsap-ease-out);
    width: 0%;
    box-shadow: 0 0 10px rgba(255, 51, 51, 0.3);
}

.scroll-progress::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8));
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-20px); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(20px); opacity: 0; }
}

/* Color Transition System */
#learner {
    background: linear-gradient(135deg, #ff3333 0%, #ff6666 100%);
    color: white;
}

#developer {
    background: linear-gradient(135deg, #ff6666 0%, #ffaaaa 50%, #f5f5f5 100%);
    color: #333;
}

#works {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 50%, #ddd 100%);
    color: #333;
}

#designer {
    background: linear-gradient(135deg, #ddd 0%, #ccc 50%, #bbb 100%);
    color: #333;
}

.math-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    opacity: 0.3;
}

.formula-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.formula {
    position: absolute;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
    color: white;
    white-space: nowrap;
    /* Animation now handled by GSAP */
}

.formula:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
.formula:nth-child(2) { top: 20%; right: 15%; animation-delay: -2s; }
.formula:nth-child(3) { top: 30%; left: 10%; animation-delay: -4s; }
.formula:nth-child(4) { top: 40%; right: 25%; animation-delay: -6s; }
.formula:nth-child(5) { top: 50%; left: 30%; animation-delay: -8s; }
.formula:nth-child(6) { top: 60%; right: 10%; animation-delay: -10s; }
.formula:nth-child(7) { top: 70%; left: 15%; animation-delay: -12s; }
.formula:nth-child(8) { top: 80%; right: 30%; animation-delay: -14s; }
.formula:nth-child(9) { top: 85%; left: 25%; animation-delay: -16s; }
.formula:nth-child(10) { top: 90%; right: 20%; animation-delay: -18s; }

/* Float animation removed - now handled by GSAP */

.mesh-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 300px;
    perspective: 1000px;
}

.mesh-object {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.1s ease;
}

.mesh-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(90deg, rgba(255,255,255,0.2) 1px, transparent 1px),
        linear-gradient(rgba(255,255,255,0.2) 1px, transparent 1px);
    background-size: 20px 20px;
    border: 2px solid rgba(255,255,255,0.4);
    border-radius: 20px;
    transform: rotateX(15deg) rotateY(-15deg);
}

.mesh-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 2;
}

.mesh-text h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 5px;
}

.mesh-text p {
    font-size: 18px;
    font-weight: 300;
    margin-bottom: 5px;
}

.mesh-text h3 {
    font-size: 28px;
    font-weight: 600;
}

.content {
    position: absolute;
    bottom: 80px;
    left: 80px;
    z-index: 10;
}

.main-title {
    font-size: var(--font-size-6xl);
    font-weight: var(--font-weight-black);
    line-height: var(--line-height-tight);
    margin-bottom: 12px;
    letter-spacing: var(--letter-spacing-tight);
    color: #000;
    text-rendering: optimizeLegibility;
}

.subtitle {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-light);
    opacity: 0.8;
    color: #666;
    letter-spacing: var(--letter-spacing-wide);
}

.whoami-text {
    position: absolute;
    top: -200px;
    right: -400px;
    font-size: 200px;
    font-weight: 900;
    opacity: 0.1;
    letter-spacing: -5px;
    transform: rotate(-5deg);
}

/* Developer Section - Code Matrix Style */

/* Code Rain Effect */
.code-rain-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    opacity: 0.3;
}

.code-column {
    position: absolute;
    top: -100%;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
    color: #ff6666;
    white-space: pre;
    /* Animation now handled by GSAP */
}

/* Code rain animation removed - now handled by GSAP */

/* Central Code Visualization */
.code-visualization {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    height: 400px;
    perspective: 1000px;
}

.code-cube {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    animation: rotateCube 20s linear infinite;
}

@keyframes rotateCube {
    0% { transform: rotateX(0deg) rotateY(0deg); }
    100% { transform: rotateX(360deg) rotateY(360deg); }
}

.code-face {
    position: absolute;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 102, 102, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'JetBrains Mono', monospace;
    font-size: 12px;
    color: #ff6666;
    backdrop-filter: blur(5px);
}

.code-face.front { transform: translateZ(150px); }
.code-face.back { transform: translateZ(-150px) rotateY(180deg); }
.code-face.right { transform: rotateY(90deg) translateZ(150px); }
.code-face.left { transform: rotateY(-90deg) translateZ(150px); }
.code-face.top { transform: rotateX(90deg) translateZ(150px); }
.code-face.bottom { transform: rotateX(-90deg) translateZ(150px); }

.philosophy {
    position: absolute;
    bottom: 100px;
    left: 80px;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-light);
    font-style: italic;
    color: #666;
    opacity: 0.9;
    letter-spacing: var(--letter-spacing-wide);
    line-height: var(--line-height-relaxed);
}

/* Works Section - Red to Orange Color Transition */
.geometric-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    background: linear-gradient(135deg, #f5f5f5 0%, #fff0f0 100%);
}

.floating-shape {
    position: absolute;
    animation: floatShapeColor 20s infinite ease-in-out;
    filter: blur(0.5px);
}

.shape-1 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    background: linear-gradient(45deg, #ff6666, #ff8888);
    box-shadow: 0 0 30px rgba(255, 102, 102, 0.3);
}

.shape-2 {
    width: 60px;
    height: 60px;
    top: 20%;
    right: 15%;
    animation-delay: -3s;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    background: linear-gradient(45deg, #ff8844, #ffaa66);
    box-shadow: 0 0 25px rgba(255, 136, 68, 0.4);
}

.shape-3 {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    top: 60%;
    left: 5%;
    animation-delay: -6s;
    background: linear-gradient(45deg, #ff6666, #ff9944);
    box-shadow: 0 0 35px rgba(255, 102, 102, 0.3);
}

.shape-4 {
    width: 40px;
    height: 40px;
    top: 70%;
    right: 20%;
    animation-delay: -9s;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    background: linear-gradient(45deg, #ffaa66, #ffcc88);
    box-shadow: 0 0 20px rgba(255, 170, 102, 0.4);
}

.shape-5 {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    top: 30%;
    left: 70%;
    animation-delay: -12s;
    background: linear-gradient(45deg, #ff8888, #ffaa66);
    box-shadow: 0 0 40px rgba(255, 136, 136, 0.3);
}

.shape-6 {
    width: 50px;
    height: 50px;
    top: 80%;
    left: 60%;
    animation-delay: -15s;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    background: linear-gradient(45deg, #ff9944, #ffbb77);
    box-shadow: 0 0 25px rgba(255, 153, 68, 0.4);
}

@keyframes floatShapeColor {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.6;
        filter: hue-rotate(0deg) blur(0.5px);
    }
    25% {
        transform: translateY(-30px) rotate(90deg) scale(1.1);
        opacity: 0.8;
        filter: hue-rotate(15deg) blur(0.3px);
    }
    50% {
        transform: translateY(-60px) rotate(180deg) scale(0.9);
        opacity: 0.7;
        filter: hue-rotate(30deg) blur(0.7px);
    }
    75% {
        transform: translateY(-30px) rotate(270deg) scale(1.05);
        opacity: 0.9;
        filter: hue-rotate(15deg) blur(0.4px);
    }
}

.projects-grid {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    width: 900px;
    z-index: 5;
}

.project-card {
    width: 280px;
    height: 200px;
    perspective: 1000px;
    cursor: pointer;
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.project-card:hover .card-inner {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-front {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 240, 240, 0.95));
    border: 2px solid rgba(255, 102, 102, 0.2);
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 30px rgba(255, 102, 102, 0.1);
}

.card-back {
    background: linear-gradient(135deg, rgba(255, 102, 102, 0.95), rgba(255, 136, 68, 0.95));
    color: white;
    transform: rotateY(180deg);
    box-shadow: 0 10px 30px rgba(255, 102, 102, 0.3);
}

.card-number {
    font-size: 14px;
    font-weight: 300;
    opacity: 0.6;
    margin-bottom: 10px;
    letter-spacing: 2px;
}

.card-front h3 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.2;
}

.card-category {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card-back p {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 15px;
    opacity: 0.9;
}

.tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.tech-stack span {
    font-size: 10px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Gallery Section - Queue Card Carousel */
.gallery-carousel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 500px;
    perspective: 1000px;
    z-index: 5;
}

.art-card {
    position: absolute;
    width: 220px;
    height: 300px;
    background: linear-gradient(135deg,
        rgba(255, 182, 193, 0.95),
        rgba(255, 192, 203, 0.9));
    border: 2px solid rgba(255, 105, 180, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    box-shadow: 0 15px 35px rgba(255, 105, 180, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    cursor: pointer;
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    overflow: hidden;
    position: relative;
}

.art-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left var(--gsap-slow) ease;
}

.art-card:hover::before {
    left: 100%;
}

.art-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 25px 50px rgba(255, 105, 180, 0.3);
    border-color: rgba(255, 105, 180, 0.6);
}

.art-card:hover .art-illustration {
    transform: scale(1.1) rotate(5deg);
}

.art-card:hover h3 {
    transform: translateY(-5px);
    text-shadow: 0 4px 15px rgba(255, 105, 180, 0.5);
}

.art-card:hover p {
    transform: translateY(-3px);
    color: rgba(255, 255, 255, 0.95);
}
    transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    top: 50%;
    left: 50%;
    transform-origin: center center;
}

/* Queue positions - cards lined up behind each other */
.card-1 {
    transform: translate(-50%, -50%) translateZ(0px) scale(1);
    z-index: 6;
    opacity: 1;
}

.card-2 {
    transform: translate(-50%, -50%) translateZ(-100px) scale(0.9);
    z-index: 5;
    opacity: 0.8;
}

.card-3 {
    transform: translate(-50%, -50%) translateZ(-200px) scale(0.8);
    z-index: 4;
    opacity: 0.6;
}

.card-4 {
    transform: translate(-50%, -50%) translateZ(-300px) scale(0.7);
    z-index: 3;
    opacity: 0.4;
}

.card-5 {
    transform: translate(-50%, -50%) translateZ(-400px) scale(0.6);
    z-index: 2;
    opacity: 0.3;
}

.card-6 {
    transform: translate(-50%, -50%) translateZ(-500px) scale(0.5);
    z-index: 1;
    opacity: 0.2;
}

/* Active card (front of queue) */
.art-card.active {
    transform: translate(-50%, -50%) translateZ(50px) scale(1.1) !important;
    z-index: 10 !important;
    opacity: 1 !important;
    box-shadow: 0 25px 50px rgba(255, 105, 180, 0.4);
    background: linear-gradient(135deg,
        rgba(255, 105, 180, 0.95),
        rgba(255, 182, 193, 0.9));
}

.art-illustration {
    width: 120px;
    height: 120px;
    border-radius: 15px;
    margin-bottom: 15px;
    background-size: cover;
    background-position: center;
    box-shadow: 0 8px 20px rgba(255, 105, 180, 0.3);
}

.mucha-1 {
    background: linear-gradient(45deg, #ff69b4, #ffb6c1);
    position: relative;
    overflow: hidden;
}

.mucha-1::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
}

.mucha-2 {
    background: linear-gradient(135deg, #dda0dd, #ff69b4);
    position: relative;
}

.mucha-2::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 10px;
}

.mucha-3 {
    background: linear-gradient(45deg, #ffb6c1, #ffc0cb);
    position: relative;
}

.mucha-3::before {
    content: '✿';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 40px;
    color: rgba(255, 255, 255, 0.7);
}

.mucha-4 {
    background: linear-gradient(135deg, #ff1493, #ff69b4);
    position: relative;
}

.mucha-4::after {
    content: '';
    position: absolute;
    top: 30%;
    left: 30%;
    width: 40%;
    height: 40%;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
}

.mucha-5 {
    background: linear-gradient(45deg, #ffc0cb, #dda0dd);
    position: relative;
}

.mucha-5::before {
    content: '❀';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 35px;
    color: rgba(255, 255, 255, 0.8);
}

.mucha-6 {
    background: linear-gradient(135deg, #ff69b4, #ff1493);
    position: relative;
}

.mucha-6::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    border: 3px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
}

.art-card h3 {
    font-size: 18px;
    font-weight: 700;
    margin: 10px 0 5px 0;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 2px 10px rgba(255, 105, 180, 0.3);
}

.art-card p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-style: italic;
}

.pink-gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        #ffeef0 0%,
        #ffe4e6 25%,
        #ffd6d9 50%,
        #ffc8cc 75%,
        #ffb6c1 100%);
    z-index: 1;
}

.floating-pink-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.pink-float {
    position: absolute;
    border-radius: 50%;
    filter: blur(1px);
    /* Animation now handled by GSAP */
}

.pink-1 {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(255, 182, 193, 0.4), rgba(255, 105, 180, 0.2));
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.pink-2 {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(255, 105, 180, 0.5), rgba(255, 182, 193, 0.3));
    top: 70%;
    right: 20%;
    animation-delay: -2s;
}

.pink-3 {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(221, 160, 221, 0.3), rgba(255, 192, 203, 0.2));
    top: 40%;
    left: 80%;
    animation-delay: -4s;
}

.pink-4 {
    width: 70px;
    height: 70px;
    background: radial-gradient(circle, rgba(255, 192, 203, 0.4), rgba(255, 105, 180, 0.2));
    bottom: 30%;
    left: 25%;
    animation-delay: -6s;
}

/* Pink float animation removed - now handled by GSAP */

.floating-geometry {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.geo-float {
    position: absolute;
    animation: geoFloatArt 12s infinite ease-in-out;
}

.geo-1 {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ffcc44, #ff9944);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    top: 15%;
    left: 20%;
    animation-delay: 0s;
    box-shadow: 0 0 20px rgba(255, 204, 68, 0.4);
}

.geo-2 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #ff9944, #ffaa66);
    border-radius: 50%;
    top: 25%;
    right: 15%;
    animation-delay: -3s;
    box-shadow: 0 0 25px rgba(255, 153, 68, 0.4);
}

.geo-3 {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ffaa66, #ffcc88);
    transform: rotate(45deg);
    top: 60%;
    left: 10%;
    animation-delay: -6s;
    box-shadow: 0 0 18px rgba(255, 170, 102, 0.4);
}

.geo-4 {
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, #ffcc88, #ffdd99);
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
    top: 70%;
    right: 25%;
    animation-delay: -9s;
    box-shadow: 0 0 22px rgba(255, 204, 136, 0.4);
}

.geo-5 {
    width: 90px;
    height: 90px;
    background: linear-gradient(45deg, #ffdd99, #ffee77);
    border-radius: 50%;
    top: 40%;
    left: 60%;
    animation-delay: -12s;
    box-shadow: 0 0 28px rgba(255, 221, 153, 0.4);
}

@keyframes geoFloatArt {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.7;
        filter: hue-rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg) scale(1.1);
        opacity: 0.9;
        filter: hue-rotate(30deg);
    }
    66% {
        transform: translateY(-40px) rotate(240deg) scale(0.9);
        opacity: 0.8;
        filter: hue-rotate(60deg);
    }
}

.art-centerpiece {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    z-index: 5;
}

.art-ring {
    position: absolute;
    border-radius: 50%;
    border: 2px solid;
    animation: ringRotate 10s infinite linear;
}

.ring-1 {
    width: 200px;
    height: 200px;
    border-color: rgba(255, 204, 68, 0.6);
    animation-duration: 10s;
}

.ring-2 {
    width: 150px;
    height: 150px;
    top: 25px;
    left: 25px;
    border-color: rgba(255, 153, 68, 0.7);
    animation-duration: 8s;
    animation-direction: reverse;
}

.ring-3 {
    width: 100px;
    height: 100px;
    top: 50px;
    left: 50px;
    border-color: rgba(255, 170, 102, 0.8);
    animation-duration: 6s;
}

.art-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ffcc44, #ff9944);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 30px rgba(255, 204, 68, 0.6);
    animation: coreGlow 3s infinite ease-in-out;
}

.core-text {
    font-size: 16px;
    font-weight: 900;
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

@keyframes ringRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes coreGlow {
    0%, 100% {
        box-shadow: 0 0 30px rgba(255, 204, 68, 0.6);
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        box-shadow: 0 0 50px rgba(255, 204, 68, 0.9);
        transform: translate(-50%, -50%) scale(1.1);
    }
}

.triangle-1 {
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-bottom: 70px solid #ffcc44;
    top: 15%;
    left: 20%;
    animation-delay: 0s;
    filter: drop-shadow(0 0 15px rgba(255, 204, 68, 0.4));
}

.triangle-2 {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 50px solid #ff9944;
    top: 70%;
    right: 25%;
    animation-delay: -5s;
    filter: drop-shadow(0 0 12px rgba(255, 153, 68, 0.4));
}

.circle-1 {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ffdd66, #ffaa44);
    top: 25%;
    right: 15%;
    animation-delay: -2s;
    box-shadow: 0 0 25px rgba(255, 221, 102, 0.4);
}

.circle-2 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ffbb55, #ff8844);
    top: 60%;
    left: 10%;
    animation-delay: -7s;
    box-shadow: 0 0 30px rgba(255, 187, 85, 0.4);
}

.line-1, .line-2, .line-3 {
    height: 3px;
    animation: lineMoveGolden 12s infinite ease-in-out;
    border-radius: 2px;
}

.line-1 {
    width: 100px;
    top: 30%;
    left: 60%;
    animation-delay: -1s;
    background: linear-gradient(90deg, #ffcc44, #ff9944);
    box-shadow: 0 0 10px rgba(255, 204, 68, 0.5);
}

.line-2 {
    width: 80px;
    top: 50%;
    left: 30%;
    animation-delay: -4s;
    background: linear-gradient(90deg, #ffdd66, #ffaa44);
    box-shadow: 0 0 8px rgba(255, 221, 102, 0.5);
}

.line-3 {
    width: 120px;
    top: 80%;
    right: 20%;
    animation-delay: -6s;
    background: linear-gradient(90deg, #ffaa44, #ff7744);
    box-shadow: 0 0 12px rgba(255, 170, 68, 0.5);
}

@keyframes geoFloatGolden {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.7;
        filter: hue-rotate(0deg) blur(0.3px);
    }
    50% {
        transform: translateY(-40px) rotate(180deg) scale(1.1);
        opacity: 0.9;
        filter: hue-rotate(20deg) blur(0.1px);
    }
}

@keyframes lineMoveGolden {
    0%, 100% {
        transform: scaleX(1) rotate(0deg);
        opacity: 0.6;
        filter: brightness(1);
    }
    50% {
        transform: scaleX(1.5) rotate(45deg);
        opacity: 0.9;
        filter: brightness(1.3);
    }
}

.gallery-grid {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    width: 800px;
    z-index: 5;
}

.gallery-item {
    width: 250px;
    height: 180px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.05);
}

.gallery-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 248, 240, 0.9));
    border: 2px solid rgba(255, 204, 68, 0.3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 25px rgba(255, 204, 68, 0.2);
    transition: all 0.3s ease;
}

.gallery-placeholder span {
    font-size: 16px;
    font-weight: 500;
    color: #cc8800;
    opacity: 0.8;
}

.gallery-item:hover .gallery-placeholder {
    background: linear-gradient(135deg, rgba(255, 204, 68, 0.95), rgba(255, 153, 68, 0.95));
    color: white;
    border-color: rgba(255, 170, 68, 0.6);
    box-shadow: 0 12px 35px rgba(255, 204, 68, 0.4);
    transform: translateY(-10px) scale(1.05);
}

.gallery-item:hover .gallery-placeholder span {
    color: white;
    opacity: 1;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Experience Section - Blue Spectrum */
.particle-network {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

#networkCanvas {
    width: 100%;
    height: 100%;
}

.timeline-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    z-index: 5;
}

.timeline {
    position: relative;
    padding: 40px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #4488ff, #66aaff, #88ccff);
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(68, 136, 255, 0.3);
}

.timeline-item {
    position: relative;
    margin: 40px 0;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-dot {
    width: 18px;
    height: 18px;
    background: linear-gradient(45deg, #4488ff, #66aaff);
    border-radius: 50%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 1), 0 0 15px rgba(68, 136, 255, 0.4);
    animation: timelinePulse 3s infinite ease-in-out;
}

@keyframes timelinePulse {
    0%, 100% {
        transform: translateX(-50%) scale(1);
        box-shadow: 0 0 0 4px rgba(255, 255, 255, 1), 0 0 15px rgba(68, 136, 255, 0.4);
    }
    50% {
        transform: translateX(-50%) scale(1.1);
        box-shadow: 0 0 0 6px rgba(255, 255, 255, 0.8), 0 0 20px rgba(68, 136, 255, 0.6);
    }
}

.timeline-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.95));
    border: 2px solid rgba(68, 136, 255, 0.2);
    border-radius: 12px;
    padding: 25px;
    width: 250px;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 25px rgba(68, 136, 255, 0.15);
    transition: all 0.3s ease;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-right: auto;
    margin-left: 50px;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: auto;
    margin-right: 50px;
}

.timeline-content:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 12px 35px rgba(68, 136, 255, 0.25);
    background: linear-gradient(135deg, rgba(68, 136, 255, 0.1), rgba(102, 170, 255, 0.1));
    border-color: rgba(68, 136, 255, 0.4);
}

.timeline-content h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #000;
}

.timeline-content h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.timeline-content p {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    margin: 0;
}

/* Designer Section - Final Transition */

.spiral-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    perspective: 1000px;
}

.golden-spiral {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.1s ease;
}

.spiral-square {
    position: absolute;
    border: 2px solid #000;
    background: rgba(255, 255, 255, 0.1);
}

.spiral-1 {
    width: 200px;
    height: 200px;
    top: 0;
    right: 0;
}

.spiral-2 {
    width: 124px;
    height: 124px;
    bottom: 0;
    right: 0;
}

.spiral-3 {
    width: 76px;
    height: 76px;
    bottom: 0;
    left: 124px;
}

.spiral-4 {
    width: 48px;
    height: 48px;
    top: 76px;
    left: 124px;
}

.spiral-5 {
    width: 28px;
    height: 28px;
    top: 76px;
    left: 172px;
}

.spiral-curve {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><path d="M200 0 Q400 0 400 200 Q400 400 200 400 Q0 400 0 200 Q0 124 76 124 Q124 124 124 76 Q124 48 152 48" stroke="%23000" stroke-width="2" fill="none"/></svg>') no-repeat center;
    background-size: contain;
    opacity: 0.6;
}

.design-text {
    position: absolute;
    top: 50%;
    left: 80px;
    transform: translateY(-50%);
    font-size: 18px;
    line-height: 1.8;
    color: #666;
}

/* Subtle Hover Effects */
.card:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Smooth Scrolling Enhancements */
html {
    scroll-behavior: smooth;
}

/* Parallax Effects */
.math-background,
.code-background {
    will-change: transform;
}

/* Enhanced Section Transitions with Parallax */
.page-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    opacity: 0;
    transition: opacity var(--gsap-slow) ease;
    pointer-events: none;
}

.page-section.transitioning::before {
    opacity: 0.1;
}

/* Smooth reveal animations for different elements */
.page-section .main-title {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
    transition: all var(--gsap-medium) var(--gsap-bounce);
    transition-delay: 0.3s;
}

.page-section.in-view .main-title {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.page-section .subtitle {
    opacity: 0;
    transform: translateX(-20px);
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    transition-delay: 0.4s;
}

.page-section.in-view .subtitle {
    opacity: 1;
    transform: translateX(0);
}

.page-section .philosophy {
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    transition-delay: 0.5s;
}

.page-section.in-view .philosophy {
    opacity: 1;
    transform: translateY(0);
}

/* Digital Art Showcase Section */
#digital-art {
    background: linear-gradient(135deg, #ddd 0%, #ccc 50%, #bbb 100%);
    color: #333;
}

.geometric-art-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    background: linear-gradient(135deg, #e8e8e8 0%, #f0f0f0 100%);
}

.floating-geometry {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.geo-float {
    position: absolute;
    animation: geoFloatArt 12s infinite ease-in-out;
}

.triangle-1 {
    width: 0;
    height: 0;
    border-left: 40px solid transparent;
    border-right: 40px solid transparent;
    border-bottom: 70px solid var(--art-blue);
    top: 15%;
    left: 20%;
    animation-delay: 0s;
    filter: drop-shadow(0 0 15px rgba(65, 105, 225, 0.4));
}

.triangle-2 {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 50px solid var(--art-cyan);
    top: 70%;
    right: 25%;
    animation-delay: -5s;
    filter: drop-shadow(0 0 12px rgba(0, 255, 255, 0.4));
}

.circle-1 {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--art-purple), var(--art-magenta));
    top: 25%;
    right: 15%;
    animation-delay: -2s;
    box-shadow: 0 0 25px rgba(138, 43, 226, 0.4);
}

.circle-2 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--art-green), var(--art-turquoise));
    top: 60%;
    left: 10%;
    animation-delay: -7s;
    box-shadow: 0 0 30px rgba(50, 205, 50, 0.4);
}

.line-1, .line-2, .line-3 {
    height: 3px;
    animation: lineMoveArt 12s infinite ease-in-out;
    border-radius: 2px;
}

.line-1 {
    width: 100px;
    top: 30%;
    left: 60%;
    animation-delay: -1s;
    background: linear-gradient(90deg, var(--art-orange), var(--art-coral));
    box-shadow: 0 0 10px rgba(255, 140, 0, 0.5);
}

.line-2 {
    width: 80px;
    top: 50%;
    left: 30%;
    animation-delay: -4s;
    background: linear-gradient(90deg, var(--art-yellow), var(--art-lime));
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

.line-3 {
    width: 120px;
    top: 80%;
    right: 20%;
    animation-delay: -6s;
    background: linear-gradient(90deg, var(--art-violet), var(--art-indigo));
    box-shadow: 0 0 12px rgba(148, 0, 211, 0.5);
}

@keyframes geoFloatArt {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.7;
        filter: hue-rotate(0deg) blur(0.3px);
    }
    50% {
        transform: translateY(-40px) rotate(180deg) scale(1.1);
        opacity: 0.9;
        filter: hue-rotate(20deg) blur(0.1px);
    }
}

@keyframes lineMoveArt {
    0%, 100% {
        transform: scaleX(1) rotate(0deg);
        opacity: 0.6;
        filter: brightness(1);
    }
    50% {
        transform: scaleX(1.5) rotate(45deg);
        opacity: 0.9;
        filter: brightness(1.3);
    }
}

.art-gallery-3d {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 600px;
    perspective: 1000px;
    z-index: 5;
}

.gallery-frame {
    position: absolute;
    width: 200px;
    height: 150px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 240, 240, 0.8));
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.frame-1 {
    top: 20%;
    left: 10%;
    transform: rotateY(-15deg) rotateX(5deg);
}

.frame-2 {
    top: 20%;
    right: 10%;
    transform: rotateY(15deg) rotateX(5deg);
}

.frame-3 {
    bottom: 20%;
    left: 10%;
    transform: rotateY(-10deg) rotateX(-5deg);
}

.frame-4 {
    bottom: 20%;
    right: 10%;
    transform: rotateY(10deg) rotateX(-5deg);
}

.gallery-frame:hover {
    transform: scale(1.1) rotateY(0deg) rotateX(0deg);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.artwork-display {
    width: 100%;
    height: 100%;
    border-radius: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.digital-1 {
    background: linear-gradient(45deg, var(--art-blue), var(--art-cyan));
}

.digital-2 {
    background: linear-gradient(45deg, var(--art-purple), var(--art-magenta));
}

.digital-3 {
    background: linear-gradient(45deg, var(--art-green), var(--art-turquoise));
}

.digital-4 {
    background: linear-gradient(45deg, var(--art-orange), var(--art-coral));
}

.art-content {
    text-align: center;
    color: white;
    z-index: 2;
}

.art-content h4 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.art-content p {
    font-size: 12px;
    opacity: 0.9;
    font-style: italic;
}

.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

/* Generative Art Lab Section */
#generative-lab {
    background: linear-gradient(135deg, #bbb 0%, #aaa 50%, #999 100%);
    color: #333;
}

.math-viz-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

#mathCanvas {
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.generative-canvas-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    height: 400px;
    z-index: 5;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

#generativeCanvas {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--art-black), var(--art-dark-gray));
}

.canvas-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px;
}

.generation-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
}

.control-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.control-group label {
    font-size: 12px;
    color: white;
    font-weight: 500;
}

.control-group input,
.control-group select,
.control-group button {
    padding: 8px 12px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-group input:hover,
.control-group select:hover,
.control-group button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.control-group button {
    background: linear-gradient(45deg, var(--primary-red), var(--primary-pink));
    font-weight: 600;
}

.control-group button:hover {
    background: linear-gradient(45deg, var(--primary-pink), var(--primary-red));
    box-shadow: 0 5px 15px rgba(255, 51, 51, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .art-gallery-3d {
        width: 90%;
        height: 400px;
    }
    
    .gallery-frame {
        width: 150px;
        height: 100px;
    }
    
    .generative-canvas-container {
        width: 90%;
        height: 300px;
    }
    
    .generation-controls {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .nav {
        padding: 15px 20px;
    }

    .nav-menu {
        gap: 15px;
        font-size: 12px;
    }

    .main-title {
        font-size: 60px;
    }

    .content {
        bottom: 40px;
        left: 40px;
    }

    .whoami-text {
        font-size: 100px;
        top: -100px;
        right: -200px;
    }

    .mesh-container,
    .tree-container,
    .cards-container,
    .spiral-container {
        width: 250px;
        height: 200px;
    }

    .code-background {
        right: 20px;
        width: 300px;
    }

    .project-info {
        right: 20px;
        width: 300px;
    }

    .design-text {
        left: 20px;
        font-size: 16px;
    }

    .philosophy {
        left: 40px;
        bottom: 150px;
        font-size: 18px;
    }

    .scroll-progress {
        height: 3px;
    }
}

/* Philosophy Section - Purple Spectrum */
.fluid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    background: linear-gradient(135deg, #f8f0ff 0%, #f0e6ff 100%);
}

.fluid-shape {
    position: absolute;
    border-radius: 50%;
    animation: fluidMovePurple 25s infinite ease-in-out;
    filter: blur(1px);
}

.fluid-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    background: linear-gradient(45deg, rgba(136, 68, 255, 0.2), rgba(170, 102, 255, 0.3));
    box-shadow: 0 0 50px rgba(136, 68, 255, 0.2);
}

.fluid-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: 15%;
    animation-delay: -8s;
    background: linear-gradient(45deg, rgba(170, 102, 255, 0.25), rgba(204, 136, 255, 0.35));
    box-shadow: 0 0 40px rgba(170, 102, 255, 0.25);
}

.fluid-3 {
    width: 250px;
    height: 250px;
    bottom: 10%;
    left: 60%;
    animation-delay: -16s;
    background: linear-gradient(45deg, rgba(153, 85, 255, 0.2), rgba(187, 119, 255, 0.3));
    box-shadow: 0 0 45px rgba(153, 85, 255, 0.2);
}

@keyframes fluidMovePurple {
    0%, 100% {
        transform: translateX(0px) translateY(0px) scale(1);
        border-radius: 50%;
        filter: hue-rotate(0deg) blur(1px);
    }
    25% {
        transform: translateX(50px) translateY(-30px) scale(1.2);
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        filter: hue-rotate(15deg) blur(0.8px);
    }
    50% {
        transform: translateX(-30px) translateY(-60px) scale(0.8);
        border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
        filter: hue-rotate(30deg) blur(1.2px);
    }
    75% {
        transform: translateX(-60px) translateY(20px) scale(1.1);
        border-radius: 40% 60% 60% 40% / 60% 40% 40% 60%;
        filter: hue-rotate(15deg) blur(0.9px);
    }
}

.philosophy-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    z-index: 5;
}

.philosophy-text {
    text-align: center;
}

.philosophy-text h2 {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 60px;
    color: #000;
}

.philosophy-principles {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.principle {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 240, 255, 0.95));
    border: 2px solid rgba(136, 68, 255, 0.2);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 30px rgba(136, 68, 255, 0.15);
    transition: all 0.3s ease;
}

.principle:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 40px rgba(136, 68, 255, 0.25);
    background: linear-gradient(135deg, rgba(136, 68, 255, 0.1), rgba(170, 102, 255, 0.1));
    border-color: rgba(136, 68, 255, 0.4);
}

.principle:nth-child(1):hover {
    box-shadow: 0 15px 40px rgba(136, 68, 255, 0.3);
}

.principle:nth-child(2):hover {
    box-shadow: 0 15px 40px rgba(170, 102, 255, 0.3);
}

.principle:nth-child(3):hover {
    box-shadow: 0 15px 40px rgba(204, 136, 255, 0.3);
}

.principle h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #000;
}

.principle p {
    font-size: 16px;
    line-height: 1.6;
    color: #666;
    margin: 0;
}

/* Contact Section - Green Spectrum */
.network-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    background: linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%);
}

.network-node {
    position: absolute;
    width: 14px;
    height: 14px;
    background: linear-gradient(45deg, #44ff88, #66ffaa);
    border-radius: 50%;
    animation: nodeFloatGreen 8s infinite ease-in-out;
    box-shadow: 0 0 15px rgba(68, 255, 136, 0.4);
}

.node-1 { top: 20%; left: 20%; animation-delay: 0s; }
.node-2 { top: 30%; right: 25%; animation-delay: -2s; }
.node-3 { top: 60%; left: 15%; animation-delay: -4s; }
.node-4 { top: 70%; right: 20%; animation-delay: -6s; }
.node-5 { top: 45%; left: 50%; animation-delay: -1s; }

.network-connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(68, 255, 136, 0.6), transparent);
    animation: connectionPulseGreen 6s infinite ease-in-out;
    border-radius: 1px;
}

.connection-1 {
    top: 25%;
    left: 20%;
    width: 200px;
    transform: rotate(15deg);
    animation-delay: 0s;
    box-shadow: 0 0 8px rgba(68, 255, 136, 0.3);
}

.connection-2 {
    top: 45%;
    left: 40%;
    width: 150px;
    transform: rotate(-20deg);
    animation-delay: -2s;
    box-shadow: 0 0 6px rgba(102, 255, 170, 0.3);
}

.connection-3 {
    top: 65%;
    left: 25%;
    width: 180px;
    transform: rotate(25deg);
    animation-delay: -4s;
    box-shadow: 0 0 7px rgba(85, 255, 153, 0.3);
}

.connection-4 {
    top: 35%;
    right: 30%;
    width: 120px;
    transform: rotate(-45deg);
    animation-delay: -1s;
    box-shadow: 0 0 5px rgba(119, 255, 187, 0.3);
}

@keyframes nodeFloatGreen {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.7;
        box-shadow: 0 0 15px rgba(68, 255, 136, 0.4);
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.9;
        box-shadow: 0 0 20px rgba(68, 255, 136, 0.6);
    }
}

@keyframes connectionPulseGreen {
    0%, 100% {
        opacity: 0.4;
        transform: scaleX(1);
        filter: brightness(1);
    }
    50% {
        opacity: 0.8;
        transform: scaleX(1.2);
        filter: brightness(1.3);
    }
}

.contact-cards {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    width: 600px;
    z-index: 5;
}

.contact-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 255, 240, 0.95));
    border: 2px solid rgba(68, 255, 136, 0.2);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 30px rgba(68, 255, 136, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
}

.contact-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 40px rgba(68, 255, 136, 0.3);
    background: linear-gradient(135deg, rgba(68, 255, 136, 0.1), rgba(102, 255, 170, 0.1));
    border-color: rgba(68, 255, 136, 0.4);
}

.contact-card:nth-child(1):hover {
    box-shadow: 0 15px 40px rgba(68, 255, 136, 0.35);
}

.contact-card:nth-child(2):hover {
    box-shadow: 0 15px 40px rgba(85, 255, 153, 0.35);
}

.contact-card:nth-child(3):hover {
    box-shadow: 0 15px 40px rgba(102, 255, 170, 0.35);
}

.contact-card:nth-child(4):hover {
    box-shadow: 0 15px 40px rgba(119, 255, 187, 0.35);
}

.contact-icon {
    font-size: 32px;
    margin-bottom: 15px;
    opacity: 0.8;
}

.contact-card h3 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #000;
}

.contact-card p {
    font-size: 14px;
    color: #666;
    margin: 0;
    font-family: 'JetBrains Mono', monospace;
}

/* Mobile Responsive Design for New Pages */
@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: 1fr;
        width: 320px;
        gap: 20px;
    }

    .project-card {
        width: 300px;
        height: 180px;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        width: 320px;
        gap: 20px;
    }

    .gallery-item {
        width: 300px;
        height: 200px;
    }

    .timeline-container {
        width: 320px;
    }

    .timeline-content {
        width: 200px;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 30px;
        margin-right: 30px;
    }

    .philosophy-content {
        width: 320px;
    }

    .philosophy-principles {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .philosophy-text h2 {
        font-size: 32px;
        margin-bottom: 40px;
    }

    .contact-cards {
        grid-template-columns: 1fr;
        width: 320px;
        gap: 20px;
    }

    .nav-menu {
        display: none;
    }

    .floating-shape,
    .geo-shape,
    .fluid-shape,
    .network-node,
    .network-connection {
        display: none;
    }
}

/* ===== ENHANCED RESPONSIVE DESIGN SYSTEM ===== */

/* Large Desktop */
@media (min-width: 1536px) {
    .nav-container {
        max-width: 1600px;
    }

    .main-title {
        font-size: calc(var(--font-size-6xl) * 1.2);
    }
}

/* Desktop */
@media (max-width: 1280px) {
    .main-title {
        font-size: var(--font-size-5xl);
    }

    .philosophy {
        font-size: var(--font-size-xl);
        bottom: var(--space-16);
        left: var(--space-10);
    }
}

/* Tablet */
@media (max-width: 1024px) {
    .nav {
        padding: var(--space-3) var(--space-6);
    }

    .nav-menu {
        gap: var(--space-4);
        font-size: var(--font-size-xs);
    }

    .main-title {
        font-size: var(--font-size-4xl);
    }

    .subtitle {
        font-size: var(--font-size-base);
    }

    .philosophy {
        font-size: var(--font-size-lg);
        bottom: var(--space-12);
        left: var(--space-8);
    }

    .art-card {
        width: 180px;
        height: 240px;
    }

    .work-card {
        width: 220px;
        height: 160px;
    }
}

/* Mobile Large */
@media (max-width: 768px) {
    .nav {
        padding: var(--space-3) var(--space-5);
    }

    .nav-menu {
        gap: var(--space-3);
        font-size: var(--font-size-xs);
    }

    .main-title {
        font-size: var(--font-size-3xl);
        text-align: center;
    }

    .subtitle {
        font-size: var(--font-size-sm);
        text-align: center;
    }

    .philosophy {
        font-size: var(--font-size-base);
        bottom: var(--space-10);
        left: var(--space-5);
        right: var(--space-5);
        text-align: center;
    }

    .content {
        bottom: var(--space-10);
        left: var(--space-5);
        right: var(--space-5);
        text-align: center;
    }

    /* Touch-friendly interactions */
    .nav-link {
        padding: var(--space-3) var(--space-4);
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    .art-card,
    .work-card {
        transform: scale(0.9);
    }

    .art-card:hover,
    .work-card:hover {
        transform: scale(0.95);
    }
}

/* Mobile Small */
@media (max-width: 640px) {
    .nav-menu {
        display: none;
    }

    .nav-container::after {
        content: 'MENU';
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
        color: #666;
        cursor: pointer;
        padding: var(--space-2) var(--space-3);
        border-radius: var(--radius-md);
        transition: all var(--gsap-fast) ease;
    }

    .nav-container:hover::after {
        background: rgba(0, 0, 0, 0.05);
        color: var(--primary-red);
    }

    .main-title {
        font-size: var(--font-size-2xl);
    }

    .philosophy {
        font-size: var(--font-size-sm);
        bottom: var(--space-8);
        left: var(--space-4);
        right: var(--space-4);
    }

    .page-loader .loader-text {
        font-size: var(--font-size-xs);
    }

    /* Optimize animations for mobile */
    .mouse-follower,
    .mouse-trail {
        display: none;
    }

    .art-card::before,
    .work-card::before {
        display: none;
    }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .mouse-follower,
    .mouse-trail,
    .page-loader {
        display: none;
    }
}

/* Universe Section - Pink Cosmic Spectrum */
.cosmic-space {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2e0a1a 0%, #3e1626 50%, #3a1a2e 100%);
    z-index: 1;
}

#starsCanvas {
    width: 100%;
    height: 100%;
}

.solar-system {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    height: 600px;
    z-index: 3;
}

.orbit {
    position: absolute;
    border: 1px solid rgba(255, 105, 180, 0.3);
    border-radius: 50%;
    animation: orbitRotate linear infinite;
}

.orbit-1 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 8s;
}

.orbit-2 {
    width: 250px;
    height: 250px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 12s;
}

.orbit-3 {
    width: 350px;
    height: 350px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 16s;
}

.orbit-4 {
    width: 450px;
    height: 450px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 20s;
}

.planet {
    position: absolute;
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(255, 105, 180, 0.4);
    animation: planetGlow 3s infinite ease-in-out;
}

.planet-1 {
    width: 12px;
    height: 12px;
    background: linear-gradient(45deg, #ff69b4, #ffb6c1);
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
}

.planet-2 {
    width: 16px;
    height: 16px;
    background: linear-gradient(45deg, #ffb6c1, #ffc0cb);
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
}

.planet-3 {
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #ffc0cb, #dda0dd);
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.planet-4 {
    width: 24px;
    height: 24px;
    background: linear-gradient(45deg, #dda0dd, #ff1493);
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
}

@keyframes orbitRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes planetGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(68, 136, 255, 0.4);
        transform: translateX(-50%) scale(1);
    }
    50% {
        box-shadow: 0 0 30px rgba(68, 136, 255, 0.7);
        transform: translateX(-50%) scale(1.1);
    }
}

.central-sun {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    z-index: 4;
}

.sun-core {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, #4488ff 0%, #2266dd 50%, #1144bb 100%);
    border-radius: 50%;
    box-shadow: 0 0 50px rgba(68, 136, 255, 0.8);
    animation: sunPulse 4s infinite ease-in-out;
}

.sun-flare {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(68, 136, 255, 0.6), transparent);
    border-radius: 50px;
    animation: flareRotate 6s infinite linear;
}

.flare-1 {
    width: 120px;
    height: 4px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 0s;
}

.flare-2 {
    width: 100px;
    height: 3px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
    animation-delay: -1.5s;
}

.flare-3 {
    width: 110px;
    height: 3px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(90deg);
    animation-delay: -3s;
}

.flare-4 {
    width: 90px;
    height: 2px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(135deg);
    animation-delay: -4.5s;
}

@keyframes sunPulse {
    0%, 100% {
        box-shadow: 0 0 50px rgba(68, 136, 255, 0.8);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 80px rgba(68, 136, 255, 1);
        transform: scale(1.1);
    }
}

@keyframes flareRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); opacity: 0.6; }
    50% { opacity: 1; }
    100% { transform: translate(-50%, -50%) rotate(360deg); opacity: 0.6; }
}

.shooting-stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.shooting-star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #4488ff;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(68, 136, 255, 0.8);
    animation: shootingStar 3s infinite linear;
}

.star-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.star-2 {
    top: 60%;
    left: 80%;
    animation-delay: -1s;
}

.star-3 {
    top: 40%;
    left: 60%;
    animation-delay: -2s;
}

@keyframes shootingStar {
    0% {
        transform: translateX(0) translateY(0) scale(0);
        opacity: 0;
    }
    10% {
        transform: translateX(20px) translateY(20px) scale(1);
        opacity: 1;
    }
    90% {
        transform: translateX(200px) translateY(200px) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(220px) translateY(220px) scale(0);
        opacity: 0;
    }
}

/* Fluid Section - Purple Liquid Art */
.liquid-art {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2d1b69 0%, #8844ff 50%, #aa66ff 100%);
    overflow: hidden;
    z-index: 1;
}

.liquid-blob {
    position: absolute;
    border-radius: 50%;
    animation: liquidFloat 20s infinite ease-in-out;
    filter: blur(2px);
}

.blob-1 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(136, 68, 255, 0.4), rgba(170, 102, 255, 0.2));
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.blob-2 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(170, 102, 255, 0.5), rgba(204, 136, 255, 0.3));
    top: 60%;
    right: 20%;
    animation-delay: -5s;
}

.blob-3 {
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(204, 136, 255, 0.4), rgba(238, 170, 255, 0.2));
    bottom: 20%;
    left: 60%;
    animation-delay: -10s;
}

.blob-4 {
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(153, 85, 255, 0.6), rgba(187, 119, 255, 0.4));
    top: 30%;
    left: 70%;
    animation-delay: -15s;
}

.blob-5 {
    width: 160px;
    height: 160px;
    background: radial-gradient(circle, rgba(187, 119, 255, 0.5), rgba(221, 153, 255, 0.3));
    top: 70%;
    left: 30%;
    animation-delay: -8s;
}

@keyframes liquidFloat {
    0%, 100% {
        transform: translateX(0px) translateY(0px) scale(1);
        border-radius: 50%;
    }
    25% {
        transform: translateX(30px) translateY(-40px) scale(1.2);
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    50% {
        transform: translateX(-20px) translateY(-80px) scale(0.8);
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
    75% {
        transform: translateX(-50px) translateY(-20px) scale(1.1);
        border-radius: 40% 60% 60% 40% / 70% 30% 60% 40%;
    }
}

.morphing-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    z-index: 3;
}

.morph-shape {
    position: absolute;
    animation: morphTransform 15s infinite ease-in-out;
}

.morph-1 {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(136, 68, 255, 0.8), rgba(170, 102, 255, 0.6));
    top: 20%;
    left: 20%;
    border-radius: 50%;
    animation-delay: 0s;
}

.morph-2 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, rgba(170, 102, 255, 0.7), rgba(204, 136, 255, 0.5));
    top: 60%;
    right: 30%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation-delay: -5s;
}

.morph-3 {
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, rgba(204, 136, 255, 0.6), rgba(238, 170, 255, 0.4));
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: -10s;
}

@keyframes morphTransform {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        border-radius: 50%;
        opacity: 0.8;
    }
    33% {
        transform: rotate(120deg) scale(1.3);
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        opacity: 1;
    }
    66% {
        transform: rotate(240deg) scale(0.7);
        border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
        opacity: 0.9;
    }
}

.fluid-text-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 20px;
    z-index: 4;
}

.fluid-letter {
    font-size: 80px;
    font-weight: 900;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0 30px rgba(136, 68, 255, 0.8);
    animation: letterWave 4s infinite ease-in-out;
    filter: blur(0.5px);
}

.fluid-letter:nth-child(1) { animation-delay: 0s; }
.fluid-letter:nth-child(2) { animation-delay: 0.2s; }
.fluid-letter:nth-child(3) { animation-delay: 0.4s; }
.fluid-letter:nth-child(4) { animation-delay: 0.6s; }
.fluid-letter:nth-child(5) { animation-delay: 0.8s; }

@keyframes letterWave {
    0%, 100% {
        transform: translateY(0px) scale(1);
        text-shadow: 0 0 30px rgba(136, 68, 255, 0.8);
        filter: blur(0.5px);
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        text-shadow: 0 0 50px rgba(170, 102, 255, 1);
        filter: blur(0.2px);
    }
}

.ripple-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.ripple {
    position: absolute;
    border: 2px solid rgba(136, 68, 255, 0.4);
    border-radius: 50%;
    animation: rippleExpand 6s infinite ease-out;
}

.ripple-1 {
    width: 100px;
    height: 100px;
    top: 30%;
    left: 20%;
    animation-delay: 0s;
}

.ripple-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 25%;
    animation-delay: -2s;
}

.ripple-3 {
    width: 120px;
    height: 120px;
    bottom: 30%;
    left: 60%;
    animation-delay: -4s;
}

@keyframes rippleExpand {
    0% {
        transform: scale(0);
        opacity: 1;
        border-width: 4px;
    }
    100% {
        transform: scale(3);
        opacity: 0;
        border-width: 1px;
    }
}

/* Network Section - Green Digital Matrix */
.neural-network {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a2e0a 0%, #1a4a1a 50%, #2a6a2a 100%);
    z-index: 1;
}

#neuralCanvas {
    width: 100%;
    height: 100%;
}

.data-streams {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.data-stream {
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(180deg, transparent, #44ff88, transparent);
    animation: dataFlow 3s infinite linear;
}

.stream-1 {
    left: 20%;
    animation-delay: 0s;
}

.stream-2 {
    left: 40%;
    animation-delay: -0.6s;
}

.stream-3 {
    left: 60%;
    animation-delay: -1.2s;
}

.stream-4 {
    left: 80%;
    animation-delay: -1.8s;
}

.stream-5 {
    left: 30%;
    animation-delay: -2.4s;
}

@keyframes dataFlow {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(calc(100vh + 100px));
        opacity: 0;
    }
}

.network-hub {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    z-index: 4;
}

.hub-core {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, #44ff88 0%, #22dd66 50%, #11bb44 100%);
    border-radius: 50%;
    box-shadow: 0 0 40px rgba(68, 255, 136, 0.8);
    animation: hubPulse 3s infinite ease-in-out;
}

.hub-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(68, 255, 136, 0.4);
    border-radius: 50%;
    animation: hubRotate linear infinite;
}

.ring-1 {
    width: 100px;
    height: 100px;
    animation-duration: 8s;
}

.ring-2 {
    width: 140px;
    height: 140px;
    animation-duration: 12s;
    animation-direction: reverse;
}

.ring-3 {
    width: 180px;
    height: 180px;
    animation-duration: 16s;
}

.hub-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: rgba(68, 255, 136, 0.6);
    border-radius: 50%;
    animation: pulseExpand 2s infinite ease-out;
}

.pulse-1 { animation-delay: 0s; }
.pulse-2 { animation-delay: -1s; }

@keyframes hubPulse {
    0%, 100% {
        box-shadow: 0 0 40px rgba(68, 255, 136, 0.8);
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        box-shadow: 0 0 60px rgba(68, 255, 136, 1);
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes hubRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes pulseExpand {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

.data-nodes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
}

.data-node {
    position: absolute;
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, rgba(68, 255, 136, 0.8), rgba(102, 255, 170, 0.6));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    color: white;
    font-family: 'JetBrains Mono', monospace;
    box-shadow: 0 0 20px rgba(68, 255, 136, 0.4);
    animation: nodeFloat 4s infinite ease-in-out;
}

.node-1 { top: 20%; left: 15%; animation-delay: 0s; }
.node-2 { top: 30%; right: 20%; animation-delay: -1s; }
.node-3 { top: 60%; left: 25%; animation-delay: -2s; }
.node-4 { top: 70%; right: 30%; animation-delay: -3s; }
.node-5 { top: 40%; left: 70%; animation-delay: -0.5s; }
.node-6 { top: 80%; left: 50%; animation-delay: -2.5s; }

@keyframes nodeFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        box-shadow: 0 0 20px rgba(68, 255, 136, 0.4);
    }
    50% {
        transform: translateY(-15px) rotate(5deg);
        box-shadow: 0 0 30px rgba(68, 255, 136, 0.7);
    }
}

/* Works Section - Pink Gradient Portfolio */
.works-gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        #ffe0e6 0%,
        #ffccd5 20%,
        #ffb3c1 40%,
        #ff99ad 60%,
        #ff8099 80%,
        #ff6685 100%);
    animation: pinkShift 15s infinite ease-in-out;
    z-index: 1;
}

.spectrum-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
    animation: layerMove 15s infinite ease-in-out;
}

.layer-1 {
    background: radial-gradient(circle at 20% 30%, rgba(255, 102, 102, 0.4), transparent 50%);
    animation-delay: 0s;
}

.layer-2 {
    background: radial-gradient(circle at 80% 20%, rgba(255, 204, 68, 0.4), transparent 50%);
    animation-delay: -3s;
}

.layer-3 {
    background: radial-gradient(circle at 60% 70%, rgba(68, 136, 255, 0.4), transparent 50%);
    animation-delay: -6s;
}

.layer-4 {
    background: radial-gradient(circle at 30% 80%, rgba(136, 68, 255, 0.4), transparent 50%);
    animation-delay: -9s;
}

.layer-5 {
    background: radial-gradient(circle at 70% 40%, rgba(68, 255, 136, 0.4), transparent 50%);
    animation-delay: -12s;
}

@keyframes spectrumShift {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

@keyframes layerMove {
    0%, 100% { transform: translateX(0px) translateY(0px) scale(1); }
    25% { transform: translateX(20px) translateY(-30px) scale(1.1); }
    50% { transform: translateX(-15px) translateY(-60px) scale(0.9); }
    75% { transform: translateX(-25px) translateY(-15px) scale(1.05); }
}

.works-showcase {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    width: 900px;
    z-index: 5;
}

.work-card {
    width: 280px;
    height: 200px;
    perspective: 1000px;
    cursor: pointer;
    animation: workFloat 6s infinite ease-in-out;
    transition: all var(--gsap-medium) var(--gsap-ease-out);
    position: relative;
    overflow: hidden;
}

.work-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        rgba(255, 105, 180, 0.1),
        transparent,
        rgba(255, 105, 180, 0.1),
        transparent
    );
    animation: workCardRotate 4s linear infinite;
    opacity: 0;
    transition: opacity var(--gsap-medium) ease;
}

.work-card:hover::before {
    opacity: 1;
}

@keyframes workCardRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.work-card-1 { animation-delay: 0s; }
.work-card-2 { animation-delay: -1s; }
.work-card-3 { animation-delay: -2s; }
.work-card-4 { animation-delay: -3s; }
.work-card-5 { animation-delay: -4s; }
.work-card-6 { animation-delay: -5s; }

.work-surface {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(255, 182, 193, 0.9),
        rgba(255, 192, 203, 0.8));
    border: 2px solid rgba(255, 105, 180, 0.3);
    border-radius: 15px;
    backdrop-filter: blur(20px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(255, 105, 180, 0.2);
}

.work-card:hover {
    transform: translateY(-15px) scale(1.05);
    animation-play-state: paused;
}

.work-card:hover .work-surface {
    transform: rotateY(10deg) rotateX(5deg);
    box-shadow: 0 25px 60px rgba(255, 105, 180, 0.5);
    background: linear-gradient(135deg,
        rgba(255, 105, 180, 0.95),
        rgba(255, 182, 193, 0.9));
    border-color: rgba(255, 105, 180, 0.6);
}

.work-card:hover .work-number {
    transform: scale(1.2) rotate(10deg);
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 105, 180, 0.8);
}

.work-card:hover h3 {
    transform: translateY(-5px);
    color: #fff;
    text-shadow: 0 2px 15px rgba(255, 105, 180, 0.6);
}

.work-card:hover .work-category {
    transform: translateY(-3px);
    color: rgba(255, 255, 255, 0.9);
}

.work-reflection {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 70%);
    border-radius: 15px;
    opacity: 0;
    transition: all 0.3s ease;
    animation: workShimmer 3s infinite ease-in-out;
}

.work-card:hover .work-reflection {
    opacity: 1;
}

@keyframes workFloat {
    0%, 100% {
        transform: translateY(0px) rotateZ(0deg);
        filter: brightness(1);
    }
    50% {
        transform: translateY(-10px) rotateZ(2deg);
        filter: brightness(1.1);
    }
}

@keyframes workShimmer {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

@keyframes pinkShift {
    0%, 100% { filter: hue-rotate(0deg) brightness(1); }
    50% { filter: hue-rotate(15deg) brightness(1.1); }
}

.work-number {
    font-size: 14px;
    font-weight: 300;
    opacity: 0.8;
    margin-bottom: 10px;
    letter-spacing: 2px;
    color: rgba(255, 255, 255, 0.9);
}

.work-surface h3 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.2;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 2px 10px rgba(255, 105, 180, 0.3);
}

.work-surface .work-category {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.8);
}

.works-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.works-float {
    position: absolute;
    border-radius: 50%;
    animation: worksFloat 10s infinite ease-in-out;
    filter: blur(1px);
}

.works-1 {
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255, 105, 180, 0.3), rgba(255, 182, 193, 0.1));
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.works-2 {
    width: 90px;
    height: 90px;
    background: radial-gradient(circle, rgba(255, 182, 193, 0.4), rgba(255, 192, 203, 0.2));
    top: 70%;
    right: 15%;
    animation-delay: -3s;
}

.works-3 {
    width: 110px;
    height: 110px;
    background: radial-gradient(circle, rgba(255, 192, 203, 0.3), rgba(221, 160, 221, 0.1));
    top: 30%;
    right: 80%;
    animation-delay: -6s;
}

.works-4 {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(221, 160, 221, 0.4), rgba(255, 105, 180, 0.2));
    bottom: 25%;
    left: 70%;
    animation-delay: -9s;
}

@keyframes worksFloat {
    0%, 100% {
        transform: translateY(0px) scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-40px) scale(1.1) rotate(180deg);
        opacity: 1;
    }
}

.prismatic-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
}

.prism {
    position: absolute;
    width: 0;
    height: 0;
    animation: prismRotate 12s infinite linear;
}

.prism-1 {
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 50px solid rgba(255, 255, 255, 0.2);
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
}

.prism-2 {
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-bottom: 40px solid rgba(255, 255, 255, 0.15);
    top: 60%;
    right: 15%;
    animation-delay: -4s;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.2));
}

.prism-3 {
    border-left: 35px solid transparent;
    border-right: 35px solid transparent;
    border-bottom: 60px solid rgba(255, 255, 255, 0.1);
    bottom: 20%;
    left: 60%;
    animation-delay: -8s;
    filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.4));
}

@keyframes prismRotate {
    0% {
        transform: rotate(0deg) scale(1);
        filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3)) hue-rotate(0deg);
    }
    100% {
        transform: rotate(360deg) scale(1.1);
        filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.5)) hue-rotate(360deg);
    }
}
