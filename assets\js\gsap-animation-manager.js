// GSAP Animation Manager - Visual Art Portfolio
class GSAPAnimationManager {
    constructor() {
        this.masterTimeline = null;
        this.scrollTriggers = [];
        this.performanceMonitor = new PerformanceMonitor();
        this.isInitialized = false;
        
        // Animation configuration
        this.config = {
            entrance: {
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            },
            scroll: {
                start: "top 80%",
                end: "bottom 20%",
                scrub: 1
            },
            hover: {
                scale: 1.05,
                rotation: 2,
                duration: 0.3
            },
            transition: {
                type: 'fade',
                duration: 0.8
            }
        };
        
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        // Register GSAP plugins
        gsap.registerPlugin(ScrollTrigger, TextPlugin);
        
        // Set global defaults
        gsap.defaults({
            duration: 1,
            ease: "power2.out"
        });
        
        // Initialize master timeline
        this.masterTimeline = gsap.timeline({
            defaults: this.config.entrance
        });
        
        // Setup performance monitoring
        this.performanceMonitor.init();
        
        // Initialize animations when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeAnimations());
        } else {
            this.initializeAnimations();
        }
        
        this.isInitialized = true;
    }

    initializeAnimations() {
        this.initPageAnimations();
        this.setupScrollTriggers();
        this.setupHoverAnimations();
        this.setupPageTransitions();
        
        console.log('GSAP Animation Manager initialized');
    }

    // Page loading animations
    initPageAnimations() {
        // Animate navigation
        gsap.from(".nav", {
            y: -100,
            opacity: 0,
            duration: 1,
            ease: "power2.out"
        });

        // Animate logo bars with stagger
        gsap.from(".bar", {
            scaleY: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: "bounce.out",
            delay: 0.5
        });

        // Animate main titles with text reveal effect
        gsap.from(".main-title", {
            y: 100,
            opacity: 0,
            duration: 1.2,
            ease: "power2.out",
            delay: 0.8
        });

        // Animate subtitles
        gsap.from(".subtitle", {
            y: 50,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            delay: 1.2
        });

        // Animate philosophy text
        gsap.from(".philosophy", {
            y: 30,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            delay: 1.5
        });
    }

    // Scroll-triggered animations
    setupScrollTriggers() {
        // Math formulas scroll-triggered entrance (main floating handled in main.js)
        gsap.utils.toArray(".formula").forEach((formula, index) => {
            gsap.fromTo(formula, 
                {
                    opacity: 0,
                    scale: 0.3,
                    rotation: Math.random() * 360,
                    y: 50
                },
                {
                    opacity: 0.4,
                    scale: 0.8 + Math.random() * 0.4,
                    rotation: Math.random() * 360,
                    y: 0,
                    duration: 2,
                    ease: "elastic.out(1, 0.5)",
                    scrollTrigger: {
                        trigger: "#learner",
                        start: "top 80%",
                        end: "bottom 20%",
                        toggleActions: "play none none reverse"
                    },
                    delay: index * 0.15
                }
            );
        });

        // Mesh object 3D animation
        ScrollTrigger.create({
            trigger: "#learner",
            start: "top center",
            end: "bottom center",
            scrub: 1,
            onUpdate: (self) => {
                const progress = self.progress;
                const meshObject = document.querySelector("#meshObject");
                if (meshObject) {
                    gsap.set(meshObject, {
                        rotationX: 15 + progress * 360 * 0.1,
                        rotationY: -15 + progress * 360 * 0.1,
                        scale: 1 + progress * 0.2
                    });
                }
            }
        });

        // Code cube enhanced animation
        ScrollTrigger.create({
            trigger: "#developer",
            start: "top center",
            end: "bottom center",
            scrub: 1,
            onUpdate: (self) => {
                const progress = self.progress;
                const codeCube = document.querySelector("#codeCube");
                if (codeCube) {
                    gsap.set(codeCube, {
                        rotationX: progress * 180,
                        rotationY: progress * 180,
                        scale: 1 + progress * 0.1
                    });
                }
            }
        });

        // Gallery cards entrance (detailed animation handled in main.js carousel)
        ScrollTrigger.create({
            trigger: "#gallery",
            start: "top 70%",
            toggleActions: "play none none reverse",
            onEnter: () => {
                // Trigger gallery background elements
                gsap.fromTo(".pink-gradient-bg", 
                    { opacity: 0, scale: 1.1 },
                    { opacity: 1, scale: 1, duration: 1.5, ease: "power2.out" }
                );
            }
        });

        // Works cards with morphing effect
        gsap.utils.toArray(".work-card").forEach((card, index) => {
            gsap.fromTo(card,
                {
                    opacity: 0,
                    y: 100,
                    rotationX: 45,
                    scale: 0.9
                },
                {
                    opacity: 1,
                    y: 0,
                    rotationX: 0,
                    scale: 1,
                    duration: 1.2,
                    ease: "power2.out",
                    scrollTrigger: {
                        trigger: "#works",
                        start: "top 70%",
                        toggleActions: "play none none reverse"
                    },
                    delay: index * 0.1
                }
            );
        });

        // Floating elements parallax (enhanced with scroll-based movement)
        gsap.utils.toArray(".pink-float, .works-float, .geo-float").forEach((element) => {
            gsap.to(element, {
                y: -80,
                rotation: 180,
                scale: 1.1,
                duration: 2,
                ease: "none",
                scrollTrigger: {
                    trigger: element.closest(".page-section"),
                    start: "top bottom",
                    end: "bottom top",
                    scrub: 1.5
                }
            });
        });
    }

    // Hover animations
    setupHoverAnimations() {
        // Navigation links
        gsap.utils.toArray(".nav-link").forEach(link => {
            const hoverTl = gsap.timeline({ paused: true });
            hoverTl.to(link, {
                scale: 1.1,
                color: "#ff3333",
                duration: 0.3,
                ease: "power2.out"
            });

            link.addEventListener("mouseenter", () => hoverTl.play());
            link.addEventListener("mouseleave", () => hoverTl.reverse());
        });

        // Art cards hover
        gsap.utils.toArray(".art-card").forEach(card => {
            const hoverTl = gsap.timeline({ paused: true });
            hoverTl.to(card, {
                scale: 1.05,
                rotationY: 5,
                z: 50,
                duration: 0.4,
                ease: "power2.out"
            });

            card.addEventListener("mouseenter", () => hoverTl.play());
            card.addEventListener("mouseleave", () => hoverTl.reverse());
        });

        // Work cards hover
        gsap.utils.toArray(".work-card").forEach(card => {
            const hoverTl = gsap.timeline({ paused: true });
            hoverTl.to(card, {
                y: -10,
                scale: 1.02,
                rotationX: -5,
                duration: 0.3,
                ease: "power2.out"
            });

            card.addEventListener("mouseenter", () => hoverTl.play());
            card.addEventListener("mouseleave", () => hoverTl.reverse());
        });

        // Enhanced formula hover effects (coordinated with main.js animations)
        gsap.utils.toArray(".formula").forEach(formula => {
            const hoverTl = gsap.timeline({ paused: true });
            hoverTl
                .to(formula, {
                    scale: 1.4,
                    opacity: 1,
                    rotation: 0,
                    color: "#ffffff",
                    textShadow: "0 0 30px rgba(255,255,255,0.9)",
                    filter: "brightness(1.5)",
                    duration: 0.4,
                    ease: "back.out(1.7)"
                })
                .to(formula, {
                    textShadow: "0 0 40px rgba(255,255,255,1)",
                    duration: 0.2,
                    ease: "power2.out"
                }, "-=0.2");

            formula.addEventListener("mouseenter", () => {
                hoverTl.play();
            });
            
            formula.addEventListener("mouseleave", () => {
                hoverTl.reverse();
            });
        });
    }

    // Page transition animations
    setupPageTransitions() {
        // Smooth section transitions
        gsap.utils.toArray(".page-section").forEach((section, index) => {
            ScrollTrigger.create({
                trigger: section,
                start: "top center",
                end: "bottom center",
                onEnter: () => this.animateSectionEnter(section),
                onLeave: () => this.animateSectionLeave(section),
                onEnterBack: () => this.animateSectionEnter(section),
                onLeaveBack: () => this.animateSectionLeave(section)
            });
        });
    }

    animateSectionEnter(section) {
        gsap.to(section, {
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: "power2.out"
        });
    }

    animateSectionLeave(section) {
        gsap.to(section, {
            opacity: 0.7,
            scale: 0.98,
            duration: 0.5,
            ease: "power2.out"
        });
    }

    // Performance optimization methods
    optimizeForDevice() {
        const deviceCapabilities = this.performanceMonitor.getDeviceCapabilities();
        
        if (deviceCapabilities.performance === 'low') {
            // Reduce animation complexity
            gsap.globalTimeline.timeScale(0.7);
            
            // Disable complex effects
            gsap.utils.toArray(".formula").forEach(formula => {
                gsap.set(formula, { willChange: "auto" });
            });
            
            console.log('Animations optimized for low-performance device');
        }
    }

    // Utility methods
    createCustomEase(name, path) {
        return CustomEase.create(name, path);
    }

    pauseAllAnimations() {
        gsap.globalTimeline.pause();
    }

    resumeAllAnimations() {
        gsap.globalTimeline.resume();
    }

    killAllScrollTriggers() {
        ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    }

    refresh() {
        ScrollTrigger.refresh();
    }

    destroy() {
        this.killAllScrollTriggers();
        this.masterTimeline.kill();
        this.performanceMonitor.destroy();
        this.isInitialized = false;
    }
}

// Performance Monitor Class
class PerformanceMonitor {
    constructor() {
        this.fps = 60;
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.isMonitoring = false;
        this.deviceCapabilities = null;
    }

    init() {
        this.detectDeviceCapabilities();
        this.startMonitoring();
    }

    detectDeviceCapabilities() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        this.deviceCapabilities = {
            webgl: !!gl,
            canvas: !!canvas.getContext('2d'),
            performance: this.benchmarkDevice(),
            memory: navigator.deviceMemory || 4,
            cores: navigator.hardwareConcurrency || 4
        };
    }

    benchmarkDevice() {
        const start = performance.now();
        
        // Simple benchmark test
        for (let i = 0; i < 100000; i++) {
            Math.sin(i) * Math.cos(i);
        }
        
        const duration = performance.now() - start;
        
        if (duration < 10) return 'high';
        if (duration < 25) return 'medium';
        return 'low';
    }

    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.monitorFrame();
    }

    monitorFrame() {
        if (!this.isMonitoring) return;
        
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        
        if (deltaTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / deltaTime);
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            // Auto-optimize if FPS drops
            if (this.fps < 30) {
                this.optimizePerformance();
            }
        }
        
        this.frameCount++;
        requestAnimationFrame(() => this.monitorFrame());
    }

    optimizePerformance() {
        // Reduce global timeline speed
        gsap.globalTimeline.timeScale(0.8);
        
        // Reduce particle counts if any
        const particles = document.querySelectorAll('.pink-float, .works-float');
        particles.forEach(particle => {
            gsap.set(particle, { display: 'none' });
        });
        
        console.warn(`Performance optimization triggered. FPS: ${this.fps}`);
    }

    getDeviceCapabilities() {
        return this.deviceCapabilities;
    }

    getFPS() {
        return this.fps;
    }

    stopMonitoring() {
        this.isMonitoring = false;
    }

    destroy() {
        this.stopMonitoring();
    }
}

// Initialize GSAP Animation Manager
let gsapManager;
document.addEventListener('DOMContentLoaded', () => {
    gsapManager = new GSAPAnimationManager();
});