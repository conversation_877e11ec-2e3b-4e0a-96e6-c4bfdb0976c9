// Creative Portfolio - Enhanced with GSAP Animations
class CreativePortfolio {
    constructor() {
        this.currentSection = 'learner';
        this.sections = ['learner', 'developer', 'gallery', 'works', 'digital-art', 'generative-lab'];
        this.currentSectionIndex = 0;
        this.gsapManager = null;

        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupScrollDetection();
        this.setupScrollAnimations();
        this.setupAnimations();
        this.setupKeyboardNavigation();
        this.generateCodeRain();
        this.setupStarField();
        this.setupNeuralNetwork();
        this.setupGalleryCarousel();
    }

    
    // Navigation with Smooth Scroll
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetSection = document.getElementById(targetId);

                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Scroll Detection
    setupScrollDetection() {
        let ticking = false;

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.updateScrollProgress();
                    this.updateCurrentSection();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }

    updateScrollProgress() {
        const scrollProgress = document.getElementById('scrollProgress');
        const scrollTop = window.scrollY;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        if (scrollProgress) {
            scrollProgress.style.width = scrollPercent + '%';
        }
    }

    updateCurrentSection() {
        const sections = document.querySelectorAll('.page-section');
        const scrollTop = window.scrollY + window.innerHeight / 2;

        sections.forEach((section, index) => {
            const sectionTop = section.offsetTop;
            const sectionBottom = sectionTop + section.offsetHeight;

            if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
                this.currentSection = section.id;
                this.currentSectionIndex = index;
                this.updateActiveNav(section.id);
            }
        });
    }

    updateActiveNav(sectionId) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + sectionId) {
                link.classList.add('active');
            }
        });
    }
    
    // Scroll-based Animations
    setupScrollAnimations() {
        window.addEventListener('scroll', () => {
            this.updateScrollBasedAnimations();
        });
    }

    updateScrollBasedAnimations() {
        const scrollTop = window.scrollY;
        const windowHeight = window.innerHeight;

        // Calculate scroll progress for each section
        const sections = document.querySelectorAll('.page-section');

        sections.forEach((section, index) => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionProgress = Math.max(0, Math.min(1,
                (scrollTop + windowHeight/2 - sectionTop) / sectionHeight
            ));

            this.animateSection(section, sectionProgress, index);
        });
    }

    animateSection(section, progress, index) {
        const sectionId = section.id;

        // Mesh object animation (Learner section)
        if (sectionId === 'learner') {
            const meshObject = section.querySelector('#meshObject');
            if (meshObject) {
                const rotation = progress * 360;
                meshObject.style.transform = `rotateX(${15 + rotation * 0.1}deg) rotateY(${-15 + rotation * 0.1}deg)`;
            }
        }

        // Code cube animation (Developer section)
        if (sectionId === 'developer') {
            const codeCube = section.querySelector('#codeCube');
            if (codeCube) {
                const rotation = progress * 180;
                codeCube.style.transform = `rotateX(${rotation}deg) rotateY(${rotation}deg)`;
            }
        }

        // Gallery carousel animation
        if (sectionId === 'gallery') {
            const artCards = section.querySelectorAll('.art-card');
            artCards.forEach((card, cardIndex) => {
                const delay = cardIndex * 0.1;
                const cardProgress = Math.max(0, progress - delay);
                if (cardProgress > 0) {
                    card.style.opacity = Math.min(1, cardProgress * 2);
                    const scale = 0.8 + cardProgress * 0.2;
                    card.style.transform = card.style.transform.replace(/scale\([^)]*\)/, `scale(${scale})`);
                }
            });

            const pinkFloats = section.querySelectorAll('.pink-float');
            pinkFloats.forEach((float, floatIndex) => {
                const delay = floatIndex * 0.15;
                const floatProgress = Math.max(0, progress - delay);
                if (floatProgress > 0) {
                    float.style.opacity = Math.min(0.8, floatProgress * 1.5);
                }
            });
        }



        // Works cards animation
        if (sectionId === 'works') {
            const workCards = section.querySelectorAll('.work-card');
            workCards.forEach((card, cardIndex) => {
                const delay = cardIndex * 0.1;
                const cardProgress = Math.max(0, progress - delay);
                if (cardProgress > 0) {
                    card.style.opacity = Math.min(1, cardProgress * 2);
                    card.style.transform = `translateY(${(1 - cardProgress) * 50}px) rotateY(${(1 - cardProgress) * 20}deg)`;
                }
            });

            const worksFloats = section.querySelectorAll('.works-float');
            worksFloats.forEach((float, floatIndex) => {
                const delay = floatIndex * 0.2;
                const floatProgress = Math.max(0, progress - delay);
                if (floatProgress > 0) {
                    float.style.opacity = Math.min(0.8, floatProgress * 1.5);
                }
            });

            const worksBackground = section.querySelector('.works-gradient-bg');
            if (worksBackground) {
                const brightness = 1 + progress * 0.2;
                worksBackground.style.filter = `brightness(${brightness})`;
            }
        }
    }
    
    // GSAP-based Floating Animations
    setupAnimations() {
        this.animateFormulasGSAP();
        this.animateFloatingElements();
        this.animateCards();
    }
    
    animateFormulasGSAP() {
        const formulas = document.querySelectorAll('.formula');
        
        formulas.forEach((formula, index) => {
            // Add optimization classes
            formula.classList.add('gsap-optimized');
            
            // Create individual timeline for each formula with enhanced GSAP features
            const formulaTl = gsap.timeline({ 
                repeat: -1, 
                yoyo: true,
                repeatDelay: Math.random() * 2
            });
            
            // Set initial random position and rotation
            gsap.set(formula, {
                rotation: Math.random() * 360,
                opacity: 0.3 + Math.random() * 0.3,
                scale: 0.8 + Math.random() * 0.4,
                transformOrigin: "center center",
                force3D: true
            });
            
            // Enhanced GSAP animation replacing CSS keyframes
            formulaTl
                .to(formula, {
                    y: -30 + Math.random() * 60,
                    x: -20 + Math.random() * 40,
                    rotation: "+=180",
                    opacity: 0.7,
                    scale: 1 + Math.random() * 0.3,
                    duration: 8 + Math.random() * 6,
                    ease: "sine.inOut",
                    force3D: true
                })
                .to(formula, {
                    rotation: "+=180",
                    opacity: 0.4,
                    scale: 0.9,
                    duration: 6 + Math.random() * 4,
                    ease: "sine.inOut",
                    force3D: true
                }, "-=2");
            
            // Enhanced mouse interaction with GSAP
            const hoverTl = gsap.timeline({ paused: true });
            hoverTl.to(formula, {
                scale: 1.5,
                rotation: 0,
                opacity: 1,
                color: "#ffffff",
                textShadow: "0 0 30px rgba(255,255,255,0.9)",
                duration: 0.4,
                ease: "back.out(2)",
                force3D: true
            });
            
            formula.addEventListener('mouseenter', () => {
                formulaTl.pause();
                hoverTl.play();
            });
            
            formula.addEventListener('mouseleave', () => {
                hoverTl.reverse().eventCallback("onReverseComplete", () => {
                    formulaTl.resume();
                });
            });
        });
    }
    
    // New method for floating elements (pink floats, geometric shapes, etc.)
    animateFloatingElements() {
        // Pink floating elements with GSAP
        const pinkFloats = document.querySelectorAll('.pink-float');
        pinkFloats.forEach((element, index) => {
            const floatTl = gsap.timeline({ repeat: -1, yoyo: true });
            
            gsap.set(element, {
                opacity: 0.6,
                scale: 1,
                rotation: Math.random() * 360
            });
            
            floatTl.to(element, {
                y: -40 + Math.random() * 20,
                x: -15 + Math.random() * 30,
                rotation: "+=360",
                scale: 1.2,
                opacity: 0.9,
                duration: 6 + Math.random() * 4,
                ease: "sine.inOut",
                delay: index * 0.8
            });
        });
        
        // Works floating elements with GSAP
        const worksFloats = document.querySelectorAll('.works-float');
        worksFloats.forEach((element, index) => {
            const floatTl = gsap.timeline({ repeat: -1, yoyo: true });
            
            gsap.set(element, {
                opacity: 0.7,
                scale: 1,
                rotation: Math.random() * 360
            });
            
            floatTl.to(element, {
                y: -35 + Math.random() * 25,
                x: -20 + Math.random() * 40,
                rotation: "+=270",
                scale: 1.15,
                opacity: 0.95,
                duration: 7 + Math.random() * 3,
                ease: "sine.inOut",
                delay: index * 1.2
            });
        });
        
        // Geometric floating elements with GSAP
        const geoFloats = document.querySelectorAll('.geo-float');
        geoFloats.forEach((element, index) => {
            const geoTl = gsap.timeline({ repeat: -1, yoyo: true });
            
            gsap.set(element, {
                opacity: 0.7,
                scale: 1,
                rotation: 0,
                filter: "hue-rotate(0deg)"
            });
            
            geoTl
                .to(element, {
                    y: -50,
                    rotation: 120,
                    scale: 1.2,
                    opacity: 0.9,
                    filter: "hue-rotate(30deg)",
                    duration: 4,
                    ease: "sine.inOut"
                })
                .to(element, {
                    y: -80,
                    rotation: 240,
                    scale: 0.9,
                    opacity: 0.8,
                    filter: "hue-rotate(60deg)",
                    duration: 4,
                    ease: "sine.inOut"
                })
                .to(element, {
                    y: -50,
                    rotation: 360,
                    scale: 1,
                    opacity: 0.7,
                    filter: "hue-rotate(0deg)",
                    duration: 4,
                    ease: "sine.inOut"
                });
        });
    }
    
    animateTreeLeaves() {
        const treeLeaves = document.querySelector('.tree-leaves');
        if (treeLeaves) {
            let swayDirection = 1;
            
            setInterval(() => {
                swayDirection *= -1;
                treeLeaves.style.transform = `translateX(-50%) rotate(${swayDirection * 2}deg)`;
            }, 2000);
        }
    }
    
    animateCards() {
        const cards = document.querySelectorAll('.card');

        cards.forEach((card) => {
            card.addEventListener('mouseenter', () => {
                card.style.transform += ' scale(1.05)';
                card.style.zIndex = '10';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = card.style.transform.replace(' scale(1.05)', '');
                card.style.zIndex = '';
            });
        });
    }

    // Enhanced Code Rain Effect with Advanced GSAP Timeline
    generateCodeRain() {
        const codeRainContainer = document.getElementById('codeRain');
        if (!codeRainContainer) return;

        const codeSnippets = [
            'function create()',
            'const vision =',
            'return dream',
            'if (inspired)',
            'for (let art',
            'async beauty',
            'Promise.art',
            'console.magic',
            'import soul',
            'export love',
            'class Artist',
            'extends Human',
            'super.create()',
            'this.passion',
            'new Vision()',
            'typeof beauty',
            'undefined limits',
            'null boundaries',
            'true creativity',
            'false limitations',
            '// Innovation',
            '=> transform',
            'while (true)',
            'break barriers',
            'continue growth'
        ];

        // Create master timeline for coordinated code rain with enhanced features
        const masterCodeTimeline = gsap.timeline({ 
            repeat: -1,
            defaults: { ease: "none" }
        });

        // Create 18 columns of falling code with enhanced GSAP timeline
        for (let i = 0; i < 18; i++) {
            const column = document.createElement('div');
            column.className = 'code-column';
            column.style.left = `${i * 5.5}%`;
            column.style.fontFamily = 'JetBrains Mono, monospace';
            column.style.fontSize = `${12 + Math.random() * 4}px`;
            column.style.color = '#ff6666';
            column.style.whiteSpace = 'pre';
            column.style.position = 'absolute';
            column.style.pointerEvents = 'none';

            // Fill column with random code snippets
            this.regenerateColumnText(column, codeSnippets);
            codeRainContainer.appendChild(column);

            // Create enhanced individual timeline for each column
            const columnTimeline = gsap.timeline({ 
                repeat: -1,
                delay: Math.random() * 2
            });
            
            // Enhanced animation sequence with multiple phases
            columnTimeline
                .fromTo(column, 
                    {
                        y: '-120%',
                        opacity: 0,
                        filter: 'blur(3px)',
                        scale: 0.8
                    },
                    {
                        y: '-20%',
                        opacity: 0.3,
                        filter: 'blur(1px)',
                        scale: 0.9,
                        duration: 0.8,
                        ease: 'power2.out'
                    }
                )
                .to(column, {
                    y: '20vh',
                    opacity: 1,
                    filter: 'blur(0px)',
                    scale: 1,
                    duration: 1.2,
                    ease: 'power1.out'
                })
                .to(column, {
                    y: '70vh',
                    opacity: 0.9,
                    duration: 4 + Math.random() * 4,
                    ease: 'none',
                    onUpdate: function() {
                        // Add glitch effect randomly
                        if (Math.random() < 0.01) {
                            gsap.to(column, {
                                x: Math.random() * 4 - 2,
                                duration: 0.1,
                                yoyo: true,
                                repeat: 1
                            });
                        }
                    }
                })
                .to(column, {
                    y: '100vh',
                    opacity: 0.6,
                    duration: 1.5,
                    ease: 'power1.in'
                })
                .to(column, {
                    y: '120vh',
                    opacity: 0,
                    filter: 'blur(2px)',
                    scale: 0.8,
                    duration: 0.8,
                    ease: 'power2.in',
                    onComplete: () => {
                        // Regenerate text for next cycle with new content
                        this.regenerateColumnText(column, codeSnippets);
                        // Random color variation
                        const colorVariations = ['#ff6666', '#ff8888', '#ffaaaa', '#ff4444'];
                        column.style.color = colorVariations[Math.floor(Math.random() * colorVariations.length)];
                    }
                });

            // Add to master timeline with more sophisticated staggering
            masterCodeTimeline.add(columnTimeline, i * 0.2 + Math.random() * 0.5);
        }

        // Enhanced periodic intensity and color variations
        const intensityTl = gsap.timeline({ repeat: -1 });
        intensityTl
            .to(codeRainContainer, {
                opacity: 0.15,
                filter: 'brightness(0.7)',
                duration: 3,
                ease: "sine.inOut"
            })
            .to(codeRainContainer, {
                opacity: 0.4,
                filter: 'brightness(1.2)',
                duration: 2,
                ease: "sine.inOut"
            })
            .to(codeRainContainer, {
                opacity: 0.3,
                filter: 'brightness(1)',
                duration: 4,
                ease: "sine.inOut"
            });

        // Add mouse interaction effects
        codeRainContainer.addEventListener('mousemove', (e) => {
            const rect = codeRainContainer.getBoundingClientRect();
            const x = (e.clientX - rect.left) / rect.width;
            const y = (e.clientY - rect.top) / rect.height;
            
            gsap.to(codeRainContainer, {
                filter: `brightness(${1 + y * 0.3}) hue-rotate(${x * 30}deg)`,
                duration: 0.3
            });
        });

        codeRainContainer.addEventListener('mouseleave', () => {
            gsap.to(codeRainContainer, {
                filter: 'brightness(1) hue-rotate(0deg)',
                duration: 1
            });
        });
    }

    // Helper method to regenerate column text
    regenerateColumnText(column, codeSnippets) {
        let columnText = '';
        const lineCount = 15 + Math.floor(Math.random() * 10);
        for (let j = 0; j < lineCount; j++) {
            columnText += codeSnippets[Math.floor(Math.random() * codeSnippets.length)] + '\n';
        }
        column.textContent = columnText;
    }

    // Star Field Animation for Universe Section
    setupStarField() {
        const canvas = document.getElementById('starsCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        let stars = [];
        let animationId;

        // Resize canvas
        const resizeCanvas = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Star class
        class Star {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 2 + 0.5;
                this.opacity = Math.random() * 0.8 + 0.2;
                this.twinkleSpeed = Math.random() * 0.02 + 0.01;
                this.twinklePhase = Math.random() * Math.PI * 2;
            }

            update() {
                this.twinklePhase += this.twinkleSpeed;
                this.opacity = 0.3 + Math.sin(this.twinklePhase) * 0.5;
            }

            draw() {
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.size);
                gradient.addColorStop(0, `rgba(68, 136, 255, ${this.opacity})`);
                gradient.addColorStop(0.5, `rgba(102, 170, 255, ${this.opacity * 0.7})`);
                gradient.addColorStop(1, `rgba(136, 204, 255, ${this.opacity * 0.3})`);
                ctx.fillStyle = gradient;
                ctx.fill();
            }
        }

        // Initialize stars
        const initStars = () => {
            stars = [];
            for (let i = 0; i < 200; i++) {
                stars.push(new Star());
            }
        };

        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            stars.forEach(star => {
                star.update();
                star.draw();
            });

            animationId = requestAnimationFrame(animate);
        };

        // Initialize
        resizeCanvas();
        initStars();
        animate();

        // Handle resize
        window.addEventListener('resize', () => {
            resizeCanvas();
            initStars();
        });

        return () => {
            cancelAnimationFrame(animationId);
        };
    }

    // Neural Network Animation for Network Section
    setupNeuralNetwork() {
        const canvas = document.getElementById('neuralCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        let nodes = [];
        let animationId;

        // Resize canvas
        const resizeCanvas = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Neural Node class
        class NeuralNode {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.vx = (Math.random() - 0.5) * 0.3;
                this.vy = (Math.random() - 0.5) * 0.3;
                this.radius = Math.random() * 3 + 2;
                this.pulsePhase = Math.random() * Math.PI * 2;
                this.pulseSpeed = Math.random() * 0.03 + 0.02;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.pulsePhase += this.pulseSpeed;

                if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
                if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
            }

            draw() {
                const pulse = 0.5 + Math.sin(this.pulsePhase) * 0.3;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius * pulse, 0, Math.PI * 2);
                const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.radius * pulse);
                gradient.addColorStop(0, `rgba(68, 255, 136, ${0.8 * pulse})`);
                gradient.addColorStop(0.5, `rgba(102, 255, 170, ${0.6 * pulse})`);
                gradient.addColorStop(1, `rgba(136, 255, 204, ${0.3 * pulse})`);
                ctx.fillStyle = gradient;
                ctx.fill();
            }
        }

        // Initialize nodes
        const initNodes = () => {
            nodes = [];
            for (let i = 0; i < 30; i++) {
                nodes.push(new NeuralNode());
            }
        };

        // Draw connections
        const drawConnections = () => {
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    const dx = nodes[i].x - nodes[j].x;
                    const dy = nodes[i].y - nodes[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 120) {
                        ctx.beginPath();
                        ctx.moveTo(nodes[i].x, nodes[i].y);
                        ctx.lineTo(nodes[j].x, nodes[j].y);
                        const opacity = 0.3 * (1 - distance / 120);
                        ctx.strokeStyle = `rgba(68, 255, 136, ${opacity})`;
                        ctx.lineWidth = 1;
                        ctx.stroke();
                    }
                }
            }
        };

        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            nodes.forEach(node => {
                node.update();
                node.draw();
            });

            drawConnections();
            animationId = requestAnimationFrame(animate);
        };

        // Initialize
        resizeCanvas();
        initNodes();
        animate();

        // Handle resize
        window.addEventListener('resize', () => {
            resizeCanvas();
            initNodes();
        });

        return () => {
            cancelAnimationFrame(animationId);
        };
    }
    
    // Keyboard Navigation with Smooth Scroll
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowDown' || e.key === 'PageDown') {
                e.preventDefault();
                const nextIndex = Math.min(this.currentSectionIndex + 1, this.sections.length - 1);
                this.scrollToSection(nextIndex);
            } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
                e.preventDefault();
                const prevIndex = Math.max(this.currentSectionIndex - 1, 0);
                this.scrollToSection(prevIndex);
            } else if (e.key === 'Home') {
                e.preventDefault();
                this.scrollToSection(0);
            } else if (e.key === 'End') {
                e.preventDefault();
                this.scrollToSection(this.sections.length - 1);
            }
        });
    }

    scrollToSection(index) {
        const targetSection = document.getElementById(this.sections[index]);
        if (targetSection) {
            targetSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    // Wheel Event for Section Snapping (Optional)
    setupWheelSnapping() {
        let isScrolling = false;

        window.addEventListener('wheel', (e) => {
            if (isScrolling) return;

            e.preventDefault();
            isScrolling = true;

            if (e.deltaY > 0) {
                // Scroll down
                const nextIndex = Math.min(this.currentSectionIndex + 1, this.sections.length - 1);
                this.scrollToSection(nextIndex);
            } else {
                // Scroll up
                const prevIndex = Math.max(this.currentSectionIndex - 1, 0);
                this.scrollToSection(prevIndex);
            }

            setTimeout(() => {
                isScrolling = false;
            }, 1000);
        }, { passive: false });
    }

    // Particle Network Animation for Experience Section
    setupParticleNetwork() {
        const canvas = document.getElementById('networkCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        let particles = [];
        let animationId;

        // Resize canvas
        const resizeCanvas = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Particle class
        class Particle {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.vx = (Math.random() - 0.5) * 0.5;
                this.vy = (Math.random() - 0.5) * 0.5;
                this.radius = Math.random() * 2 + 1;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;

                if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
                if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
            }

            draw() {
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.radius);
                gradient.addColorStop(0, 'rgba(68, 136, 255, 0.8)');
                gradient.addColorStop(1, 'rgba(102, 170, 255, 0.4)');
                ctx.fillStyle = gradient;
                ctx.fill();
            }
        }

        // Initialize particles
        const initParticles = () => {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push(new Particle());
            }
        };

        // Draw connections
        const drawConnections = () => {
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < 100) {
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        const opacity = 0.4 * (1 - distance / 100);
                        ctx.strokeStyle = `rgba(68, 136, 255, ${opacity})`;
                        ctx.lineWidth = 1.5;
                        ctx.stroke();
                    }
                }
            }
        };

        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });

            drawConnections();
            animationId = requestAnimationFrame(animate);
        };

        // Initialize
        resizeCanvas();
        initParticles();
        animate();

        // Handle resize
        window.addEventListener('resize', () => {
            resizeCanvas();
            initParticles();
        });

        // Cleanup function
        return () => {
            cancelAnimationFrame(animationId);
        };
    }

    // Enhanced Gallery Carousel with GSAP ScrollTrigger and Advanced Animations
    setupGalleryCarousel() {
        const artCards = document.querySelectorAll('.art-card');
        if (artCards.length === 0) return;

        let currentQueue = [0, 1, 2, 3, 4, 5]; // Initial queue order
        let rotationTimeline = gsap.timeline({ repeat: -1, paused: false });
        let isScrollTriggered = false;

        // Enhanced card positioning with GSAP ScrollTrigger integration
        const updateCardPositions = (animated = true) => {
            artCards.forEach((card, index) => {
                const queuePosition = currentQueue.indexOf(index);
                card.classList.remove('active');
                
                // Add optimization classes
                card.classList.add('gsap-optimized', 'gsap-3d');

                // Enhanced GSAP animation for card positioning with 3D effects
                const zPosition = -queuePosition * 120;
                const scale = 1 - queuePosition * 0.08;
                const opacity = 1 - queuePosition * 0.12;
                const rotationY = queuePosition * 2;
                const blur = queuePosition * 0.5;

                if (animated) {
                    gsap.to(card, {
                        z: zPosition,
                        scale: scale,
                        opacity: opacity,
                        rotationY: rotationY,
                        filter: `blur(${blur}px)`,
                        duration: 1.2,
                        ease: "power2.out",
                        transformOrigin: "center center",
                        force3D: true
                    });
                } else {
                    gsap.set(card, {
                        z: zPosition,
                        scale: scale,
                        opacity: opacity,
                        rotationY: rotationY,
                        filter: `blur(${blur}px)`,
                        force3D: true
                    });
                }

                // Enhanced active card styling
                if (queuePosition === 0) {
                    card.classList.add('active');
                    const activeTl = gsap.timeline();
                    activeTl
                        .to(card, {
                            z: 80,
                            scale: 1.15,
                            opacity: 1,
                            rotationY: 0,
                            filter: 'blur(0px)',
                            duration: 1.2,
                            ease: "back.out(1.4)",
                            force3D: true
                        })
                        .to(card, {
                            boxShadow: "0 25px 60px rgba(255, 105, 180, 0.5)",
                            duration: 0.5,
                            ease: "power2.out"
                        }, "-=0.8");
                }
            });
        };

        // Enhanced rotation with smooth transitions
        const rotateQueue = () => {
            // Create transition effect before rotation
            const transitionTl = gsap.timeline();
            
            transitionTl
                .to(artCards, {
                    scale: "-=0.05",
                    duration: 0.3,
                    ease: "power2.inOut"
                })
                .call(() => {
                    // Move the first card to the back of the queue
                    const frontCard = currentQueue.shift();
                    currentQueue.push(frontCard);
                    updateCardPositions();
                })
                .to(artCards, {
                    scale: "+=0.05",
                    duration: 0.4,
                    ease: "power2.out"
                }, "-=0.2");
        };

        // ScrollTrigger integration for gallery entrance
        ScrollTrigger.create({
            trigger: "#gallery",
            start: "top 70%",
            end: "bottom 30%",
            onEnter: () => {
                if (!isScrollTriggered) {
                    this.animateGalleryEntrance(artCards);
                    isScrollTriggered = true;
                }
            },
            onLeave: () => {
                // Pause rotation when leaving gallery section
                rotationTimeline.pause();
            },
            onEnterBack: () => {
                // Resume rotation when entering back
                rotationTimeline.resume();
            }
        });

        // Initial setup with entrance animation
        gsap.set(artCards, {
            opacity: 0,
            scale: 0.5,
            z: -500,
            rotationY: 45
        });

        // Create enhanced rotation timeline
        rotationTimeline
            .to({}, { 
                duration: 4, 
                onComplete: rotateQueue,
                ease: "none"
            })
            .repeat(-1);

        // Enhanced hover interactions with GSAP
        const carousel = document.querySelector('.gallery-carousel');
        if (carousel) {
            let hoverTl = gsap.timeline({ paused: true });
            
            hoverTl
                .to(artCards, {
                    scale: "+=0.03",
                    duration: 0.4,
                    ease: "power2.out",
                    stagger: 0.05
                })
                .to(carousel, {
                    filter: "brightness(1.1) saturate(1.2)",
                    duration: 0.3,
                    ease: "power2.out"
                }, "-=0.4");

            carousel.addEventListener('mouseenter', () => {
                rotationTimeline.pause();
                hoverTl.play();
            });

            carousel.addEventListener('mouseleave', () => {
                rotationTimeline.resume();
                hoverTl.reverse();
            });
        }

        // Enhanced click interactions with smooth transitions
        artCards.forEach((card, index) => {
            card.addEventListener('click', () => {
                const cardIndex = parseInt(card.className.match(/card-(\d+)/)[1]) - 1;
                const currentPos = currentQueue.indexOf(cardIndex);
                
                if (currentPos !== 0) {
                    // Create click animation
                    const clickTl = gsap.timeline();
                    clickTl
                        .to(card, {
                            scale: 1.2,
                            duration: 0.2,
                            ease: "power2.out"
                        })
                        .to(card, {
                            scale: 1,
                            duration: 0.3,
                            ease: "back.out(1.7)"
                        })
                        .call(() => {
                            // Rearrange queue to bring clicked card to front
                            currentQueue.splice(currentPos, 1);
                            currentQueue.unshift(cardIndex);
                            updateCardPositions();
                            rotationTimeline.restart();
                        }, null, "-=0.2");
                }
            });

            // Individual card hover effects
            const cardHoverTl = gsap.timeline({ paused: true });
            cardHoverTl.to(card, {
                y: -10,
                rotationX: 5,
                duration: 0.3,
                ease: "power2.out"
            });

            card.addEventListener('mouseenter', () => cardHoverTl.play());
            card.addEventListener('mouseleave', () => cardHoverTl.reverse());
        });
    }

    // Gallery entrance animation method
    animateGalleryEntrance(artCards) {
        const entranceTl = gsap.timeline();
        
        entranceTl
            .to(artCards, {
                opacity: 1,
                scale: 1,
                z: 0,
                rotationY: 0,
                duration: 1.5,
                ease: "power2.out",
                stagger: {
                    amount: 1,
                    from: "start"
                }
            })
            .call(() => {
                updateCardPositions(false);
            }, null, "-=0.5")
            .from(".floating-pink-elements .pink-float", {
                opacity: 0,
                scale: 0,
                rotation: 180,
                duration: 1,
                ease: "back.out(1.7)",
                stagger: 0.2
            }, "-=1");
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CreativePortfolio();
});

// Add some visual effects on load
window.addEventListener('load', () => {
    document.body.style.opacity = '1';
    
    // Animate logo bars on load
    const bars = document.querySelectorAll('.bar');
    bars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.transform = 'scaleY(1.5)';
            setTimeout(() => {
                bar.style.transform = 'scaleY(1)';
            }, 200);
        }, index * 100);
    });
});
