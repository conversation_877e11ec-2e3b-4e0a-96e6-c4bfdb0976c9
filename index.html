<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WHOAMI - Creative Developer</title>
    <meta name="description" content="Knowledge is Virtue. Creation is Vitality. Beauty is Purpose.">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body class="loading">
    <!-- Page Loader -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="loader-bars">
                <div class="loader-bar"></div>
                <div class="loader-bar"></div>
                <div class="loader-bar"></div>
                <div class="loader-bar"></div>
                <div class="loader-bar"></div>
            </div>
            <div class="loader-text">LOADING EXPERIENCE</div>
        </div>
    </div>

    <!-- Mouse Follower -->
    <div class="mouse-follower" id="mouseFollower"></div>
    <div class="mouse-trail" id="mouseTrail"></div>

    <!-- Smooth Scroll Container -->
    <div class="scroll-container">
        <!-- Page Sections -->
        <section id="learner" class="page-section">
        <!-- Mathematical Background -->
        <div class="math-background">
            <div class="formula-container">
                <span class="formula">∂f(x,y)/∂x</span>
                <span class="formula">∇²u = 0</span>
                <span class="formula">∫∫∫ dV</span>
                <span class="formula">lim(x→∞)</span>
                <span class="formula">∑(n=1 to ∞)</span>
                <span class="formula">∂²u/∂t²</span>
                <span class="formula">grad_DESCENT</span>
                <span class="formula">∇ × F</span>
                <span class="formula">∮ F·dr</span>
                <span class="formula">∂/∂t</span>
            </div>
        </div>
        
        <!-- 3D Mesh Object -->
        <div class="mesh-container">
            <div class="mesh-object" id="meshObject">
                <div class="mesh-grid"></div>
                <div class="mesh-text">
                    <h2>Knowledge</h2>
                    <p>is</p>
                    <h3>Virtue</h3>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="content">
            <div class="title-section">
                <h1 class="main-title">A Learner</h1>
                <p class="subtitle">to my Blog</p>
            </div>
            <div class="whoami-text">WHOAMI</div>
        </div>
    </section>

    <section id="developer" class="page-section">
        <!-- Code Rain Background -->
        <div class="code-rain-container" id="codeRain"></div>

        <!-- Central Code Visualization -->
        <div class="code-visualization">
            <div class="code-cube" id="codeCube">
                <div class="code-face front">
                    <pre>function create() {
  const vision = new Idea();
  return vision.materialize();
}</pre>
                </div>
                <div class="code-face back">
                    <pre>class Innovation {
  constructor() {
    this.potential = Infinity;
  }
}</pre>
                </div>
                <div class="code-face right">
                    <pre>const transform = (dream) => {
  return dream
    .code()
    .compile()
    .execute();
}</pre>
                </div>
                <div class="code-face left">
                    <pre>while (learning) {
  skills.push(newKnowledge);
  creativity.expand();
}</pre>
                </div>
                <div class="code-face top">
                    <pre>export default {
  passion: true,
  dedication: 100,
  innovation: 'endless'
}</pre>
                </div>
                <div class="code-face bottom">
                    <pre>// Creation is Vitality
const life = code + art;
return life;</pre>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="title-section">
                <h1 class="main-title">A Developer</h1>
                <p class="subtitle">to my Blog</p>
            </div>
            <div class="philosophy">Creation is Vitality.</div>
        </div>
    </section>



    <section id="gallery" class="page-section">
        <!-- Pink Gradient Background -->
        <div class="pink-gradient-bg"></div>

        <!-- Queue Card Carousel -->
        <div class="gallery-carousel">
            <div class="art-card card-1 active">
                <div class="art-illustration mucha-1"></div>
                <h3>Art Nouveau</h3>
                <p>Elegant Beauty</p>
            </div>
            <div class="art-card card-2">
                <div class="art-illustration mucha-2"></div>
                <h3>Floral Dreams</h3>
                <p>Nature's Grace</p>
            </div>
            <div class="art-card card-3">
                <div class="art-illustration mucha-3"></div>
                <h3>Botanical</h3>
                <p>Organic Forms</p>
            </div>
            <div class="art-card card-4">
                <div class="art-illustration mucha-4"></div>
                <h3>Feminine</h3>
                <p>Graceful Lines</p>
            </div>
            <div class="art-card card-5">
                <div class="art-illustration mucha-5"></div>
                <h3>Decorative</h3>
                <p>Ornamental</p>
            </div>
            <div class="art-card card-6">
                <div class="art-illustration mucha-6"></div>
                <h3>Ethereal</h3>
                <p>Mystical Aura</p>
            </div>
        </div>

        <!-- Floating Pink Elements -->
        <div class="floating-pink-elements">
            <div class="pink-float pink-1"></div>
            <div class="pink-float pink-2"></div>
            <div class="pink-float pink-3"></div>
            <div class="pink-float pink-4"></div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="title-section">
                <h1 class="main-title">Gallery</h1>
                <p class="subtitle">Visual Spectrum</p>
            </div>
            <div class="philosophy">Color is Language.</div>
        </div>
    </section>







    <section id="works" class="page-section">
        <!-- Pink Gradient Background -->
        <div class="works-gradient-bg"></div>

        <!-- Portfolio Showcase -->
        <div class="works-showcase">
            <div class="work-card work-card-1">
                <div class="work-surface">
                    <div class="work-number">01</div>
                    <h3>Digital Portfolio</h3>
                    <span class="work-category">Web Design</span>
                </div>
                <div class="work-reflection"></div>
            </div>

            <div class="work-card work-card-2">
                <div class="work-surface">
                    <div class="work-number">02</div>
                    <h3>Mobile Experience</h3>
                    <span class="work-category">App Design</span>
                </div>
                <div class="work-reflection"></div>
            </div>

            <div class="work-card work-card-3">
                <div class="work-surface">
                    <div class="work-number">03</div>
                    <h3>Brand Identity</h3>
                    <span class="work-category">Branding</span>
                </div>
                <div class="work-reflection"></div>
            </div>

            <div class="work-card work-card-4">
                <div class="work-surface">
                    <div class="work-number">04</div>
                    <h3>E-commerce Platform</h3>
                    <span class="work-category">Web App</span>
                </div>
                <div class="work-reflection"></div>
            </div>

            <div class="work-card work-card-5">
                <div class="work-surface">
                    <div class="work-number">05</div>
                    <h3>Generative Art</h3>
                    <span class="work-category">Digital Art</span>
                </div>
                <div class="work-reflection"></div>
            </div>

            <div class="work-card work-card-6">
                <div class="work-surface">
                    <div class="work-number">06</div>
                    <h3>AR Experience</h3>
                    <span class="work-category">AR/VR</span>
                </div>
                <div class="work-reflection"></div>
            </div>
        </div>

        <!-- Pink Floating Elements -->
        <div class="works-floating-elements">
            <div class="works-float works-1"></div>
            <div class="works-float works-2"></div>
            <div class="works-float works-3"></div>
            <div class="works-float works-4"></div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="title-section">
                <h1 class="main-title">Works</h1>
                <p class="subtitle">Creative Spectrum</p>
            </div>
            <div class="philosophy">Art is Everything.</div>
        </div>
    </section>

    <!-- Digital Art Showcase Section -->
    <section id="digital-art" class="page-section">
        <!-- Dynamic Geometric Background -->
        <div class="geometric-art-background">
            <div class="floating-geometry">
                <div class="geo-float triangle-1"></div>
                <div class="geo-float triangle-2"></div>
                <div class="geo-float circle-1"></div>
                <div class="geo-float circle-2"></div>
                <div class="geo-float line-1"></div>
                <div class="geo-float line-2"></div>
                <div class="geo-float line-3"></div>
            </div>
        </div>

        <!-- 3D Art Gallery Space -->
        <div class="art-gallery-3d">
            <div class="gallery-frame frame-1">
                <div class="artwork-display digital-1">
                    <div class="art-content">
                        <h4>Neural Networks</h4>
                        <p>AI-Generated Patterns</p>
                    </div>
                </div>
            </div>
            <div class="gallery-frame frame-2">
                <div class="artwork-display digital-2">
                    <div class="art-content">
                        <h4>Fractal Dreams</h4>
                        <p>Mathematical Beauty</p>
                    </div>
                </div>
            </div>
            <div class="gallery-frame frame-3">
                <div class="artwork-display digital-3">
                    <div class="art-content">
                        <h4>Pixel Poetry</h4>
                        <p>Digital Minimalism</p>
                    </div>
                </div>
            </div>
            <div class="gallery-frame frame-4">
                <div class="artwork-display digital-4">
                    <div class="art-content">
                        <h4>Color Symphony</h4>
                        <p>Chromatic Harmony</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mouse Follower Particles -->
        <div class="particle-system" id="artParticles"></div>

        <!-- Main Content -->
        <div class="content">
            <div class="title-section">
                <h1 class="main-title">Digital Art</h1>
                <p class="subtitle">Computational Creativity</p>
            </div>
            <div class="philosophy">Code becomes Canvas.</div>
        </div>
    </section>

    <!-- Generative Art Lab Section -->
    <section id="generative-lab" class="page-section">
        <!-- Mathematical Visualization Background -->
        <div class="math-viz-background">
            <canvas id="mathCanvas"></canvas>
        </div>

        <!-- Interactive Art Canvas -->
        <div class="generative-canvas-container">
            <canvas id="generativeCanvas"></canvas>
            <div class="canvas-overlay">
                <div class="generation-controls">
                    <div class="control-group">
                        <label>Complexity</label>
                        <input type="range" id="complexitySlider" min="1" max="10" value="5">
                    </div>
                    <div class="control-group">
                        <label>Color Palette</label>
                        <select id="colorPalette">
                            <option value="warm">Warm</option>
                            <option value="cool">Cool</option>
                            <option value="monochrome">Monochrome</option>
                            <option value="rainbow">Rainbow</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <button id="generateBtn">Generate New</button>
                        <button id="saveBtn">Save Art</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="title-section">
                <h1 class="main-title">Generative Lab</h1>
                <p class="subtitle">Algorithmic Expression</p>
            </div>
            <div class="philosophy">Mathematics is Art.</div>
        </div>
    </section>

    </div> <!-- End scroll-container -->

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-container">
            <div class="nav-logo">
                <div class="logo-bars">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
            <div class="nav-menu">
                <a href="#learner" class="nav-link interactive-glow active">WHOAMI</a>
                <span class="nav-separator">/</span>
                <a href="#developer" class="nav-link interactive-glow">DEVELOPER</a>
                <span class="nav-separator">/</span>
                <a href="#gallery" class="nav-link interactive-glow">GALLERY</a>
                <span class="nav-separator">/</span>
                <a href="#works" class="nav-link interactive-glow">WORKS</a>
                <span class="nav-separator">/</span>
                <a href="#digital-art" class="nav-link interactive-glow">DIGITAL ART</a>
                <span class="nav-separator">/</span>
                <a href="#generative-lab" class="nav-link interactive-glow">GENERATIVE</a>
            </div>
            <div class="nav-github">
                <a href="https://github.com" class="github-link">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                </a>
            </div>
        </div>
    </nav>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- GSAP Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/MorphSVGPlugin.min.js"></script>
    
    <script src="assets/js/gsap-animation-manager.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
